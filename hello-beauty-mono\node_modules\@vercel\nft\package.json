{"name": "@vercel/nft", "version": "0.29.4", "repository": "vercel/nft", "license": "MIT", "main": "./out/index.js", "types": "./out/index.d.ts", "bin": {"nft": "./out/cli.js"}, "files": ["out"], "scripts": {"build": "tsc", "git-pre-commit": "prettier --write . && git add -A", "prepublishOnly": "tsc && rm out/utils/*.d.ts && rm out/tsconfig.tsbuildinfo", "prettier-check": "prettier --check .", "prettier-fix": "prettier --write .", "test": "jest --verbose", "test-verbose": "tsc --sourceMap && jest --verbose --coverage --globals \"{\\\"coverage\\\":true}\""}, "prettier": "@vercel/style-guide/prettier", "dependencies": {"@mapbox/node-pre-gyp": "^2.0.0", "@rollup/pluginutils": "^5.1.3", "acorn": "^8.6.0", "acorn-import-attributes": "^1.9.5", "async-sema": "^3.1.1", "bindings": "^1.4.0", "estree-walker": "2.0.2", "glob": "^10.4.5", "graceful-fs": "^4.2.9", "node-gyp-build": "^4.2.2", "picomatch": "^4.0.2", "resolve-from": "^5.0.0"}, "devDependencies": {"@aws-sdk/client-s3": "^3.787.0", "@azure/cosmos": "^4.3.0", "@bugsnag/js": "^8.2.0", "@datadog/pprof": "^5.2.0", "@ffmpeg-installer/ffmpeg": "^1.1.0", "@google-cloud/bigquery": "^7.9.2", "@google-cloud/firestore": "^7.6.0", "@opentelemetry/api": "^1.7.0", "@sentry/node": "^9.2.0", "@tpluscode/sparql-builder": "^0.3.12", "@types/bindings": "^1.3.0", "@types/estree": "^0.0.47", "@types/glob": "^7.1.2", "@types/graceful-fs": "^4.1.5", "@types/node": "^14.14.37", "@types/picomatch": "^4.0.0", "@vercel/git-hooks": "^1.0.0", "@vercel/style-guide": "^5.2.0", "analytics-node": "^6.2.0", "apollo-server-express": "^3.13.0", "argon2": "^0.41.1", "auth0": "^3.0.1", "axios": "^1.8.2", "azure-storage": "^2.10.3", "bcrypt": "^5.0.1", "browserify-middleware": "^8.1.1", "bull": "^4.16.5", "bullmq": "^5.48.0", "camaro": "^6.1.0", "chromeless": "^1.5.2", "consolidate": "^1.0.4", "copy": "^0.3.2", "cowsay": "^1.4.0", "es-get-iterator": "^1.1.0", "esbuild": "^0.25.0", "esm": "^3.2.25", "express": "^4.21.2", "fast-glob": "^3.1.1", "fetch-h2": "^3.0.2", "firebase": "^11.2.0", "firebase-admin": "^13.2.0", "fluent-ffmpeg": "^2.1.2", "geo-tz": "^7.0.1", "geoip-lite": "^1.4.10", "graphql": "^16.10.0", "highlights": "^3.1.6", "hot-shots": "^10.2.1", "ioredis": "^5.6.0", "isomorphic-unfetch": "^4.0.2", "jest": "^29.7.0", "jimp": "^1.6.0", "jugglingdb": "^2.0.1", "koa": "^2.16.1", "leveldown": "^5.6.0", "lighthouse": "^12.3.0", "loopback": "^3.26.0", "mailgun": "^0.5.0", "mariadb": "^3.4.1", "memcached": "^2.2.2", "microtime": "^3.0.0", "mongoose": "^8.13.2", "mysql": "^2.17.1", "oracledb": "^6.2.0", "paraphrase": "1.8.0", "passport": "^0.7.0", "passport-google-oauth": "^2.0.0", "passport-trakt": "^1.0.4", "path-platform": "^0.11.15", "pdf2json": "^3.1.5", "pdfkit": "^0.16.0", "pg": "^8.13.3", "phantomjs-prebuilt": "^2.1.16", "pixelmatch": "^5.2.1", "playwright-core": "^1.17.1", "polyfill-library": "3.93.0", "prettier": "^3.2.5", "prismjs": "^1.30.0", "pug": "^3.0.3", "react": "^16.14.0", "react-dom": "^16.14.0", "redis": "^3.1.1", "remark-parse": "^11.0.0", "remark-prism": "^1.3.6", "rxjs": "^7.8.2", "saslprep": "^1.0.3", "semver": "^7.5.2", "sequelize": "^6.29.0", "serialport": "^13.0.0", "sharp": "^0.34.1", "shiki": "^0.14.5", "socket.io": "^2.4.0", "socket.io-client": "^2.2.0", "stripe": "^18.0.0", "swig": "^1.4.2", "tiny-json-http": "^7.1.2", "twilio": "^5.5.2", "typescript": "^5.7.2", "uglify-js": "^3.6.0", "unified": "^11.0.5", "vm2": "^3.9.18", "vue": "^3.5.13", "when": "^3.7.8", "zeromq": "^6.0.0-beta.19"}, "packageManager": "npm@10.2.5", "engines": {"node": ">=18"}, "publishConfig": {"access": "public"}}