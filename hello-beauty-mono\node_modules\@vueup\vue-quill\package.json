{"name": "@vueup/vue-quill", "version": "1.2.0", "description": "Vue 3 rich text editor based on Quill.", "main": "index.js", "module": "dist/vue-quill.esm-bundler.js", "unpkg": "dist/vue-quill.global.prod.js", "jsdelivr": "dist/vue-quill.global.prod.js", "types": "dist/vue-quill.d.ts", "files": ["index.js", "dist"], "buildOptions": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "formats": ["esm-bundler", "esm-browser", "cjs", "global"]}, "repository": {"type": "git", "url": "git+https://github.com/vueup/vue-quill.git"}, "keywords": ["vue-quill", "vue 3 quill component", "vue 3 quill editor", "vue 3 text editor", "vue 3 rich text editor", "vue 3 web editor", "vue 3 editor", "vue 3 wysiwyg", "vue 3 wysiwyg editor"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vueup/vue-quill/issues"}, "homepage": "https://vueup.github.io/vue-quill/", "dependencies": {"quill": "^1.3.7", "quill-delta": "^4.2.2"}, "peerDependencies": {"vue": "^3.2.41"}}