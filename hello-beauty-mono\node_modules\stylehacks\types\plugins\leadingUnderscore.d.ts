export = LeadingUnderscore;
declare class LeadingUnderscore extends BasePlugin {
    /** @param {import('postcss').Result=} result */
    constructor(result?: import("postcss").Result | undefined);
    /**
     * @param {import('postcss').Declaration} decl
     * @return {void}
     */
    detect(decl: import("postcss").Declaration): void;
}
import BasePlugin = require("../plugin");
//# sourceMappingURL=leadingUnderscore.d.ts.map