"use client";
import Link from "next/link";
import { useEffect, useState } from "react";
import { Icon } from "@iconify/react";

function decodeHTMLEntities(text) {
  const textarea = document.createElement("textarea");
  textarea.innerHTML = text;
  return textarea.value;
}

export default function Blog() {
  const [articles, setArticles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  const getArticles = async (pageNum = 1, reset = false) => {
    try {
      if (reset) setLoading(true);

      const res = await fetch(`/api/wp/posts?page=${pageNum}&limit=10`);
      const data = await res.json();

      if (reset) {
        setArticles(Array.isArray(data) ? data : []);
      } else {
        setArticles(prev => [...prev, ...(Array.isArray(data) ? data : [])]);
      }

      setHasMore(Array.isArray(data) && data.length === 10);
      setLoading(false);
    } catch (error) {
      setLoading(false);
      setError(error);
      console.log(error);
    }
  };

  const loadMore = () => {
    const nextPage = page + 1;
    setPage(nextPage);
    getArticles(nextPage, false);
  };

  useEffect(() => {
    getArticles(1, true);
  }, []);
  return (
    <div className="max-w-[480px] mx-auto overflow-x-hidden overflow-clip">
      <div className="grid-bg min-h-screen">
        <div className="h-16 flex items-center px-4 max-w-[480px]">
          <button
            onClick={() => {
              window.history.back();
            }}
          >
            <img src="/icons/arrow-left.svg" alt="Logo" className="" />
          </button>
          <Link href="/" className="mx-auto">
            <img src="/logo.png" alt="Logo" className="h-8" />
          </Link>
        </div>

        <div className="px-4 py-6">
          <div className="relative mb-6">
            <h3 className="text-4xl font-semibold z-50 pl-6 text-hb-pink">
              Articles
            </h3>
            <div className="bg-hb-pink-light-2 -z-10 h-6 w-40 absolute top-0 left-0"></div>
            <div className="bottom-0 left-0 h-[6px] w-16 rounded-full bg-hb-pink ml-6"></div>
          </div>

          {loading && articles.length === 0 && (
            <div className="text-center py-6">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-hb-pink mx-auto"></div>
              <p className="mt-2 text-gray-500">Memuat artikel...</p>
            </div>
          )}

          {error && (
            <div className="text-center py-6">
              <p className="text-red-500">Gagal memuat artikel</p>
            </div>
          )}

          {articles.length === 0 && !loading && (
            <div className="text-center py-6">
              <p className="text-gray-500">Belum ada artikel</p>
            </div>
          )}

          <div className="space-y-6">
            {articles.map((article, index) => (
              <div key={index} className="bg-white rounded-lg shadow-sm border overflow-hidden">
                {article.featured_media_url && (
                  <img
                    src={article.featured_media_url}
                    alt={article.title.rendered}
                    className="w-full h-48 object-cover"
                  />
                )}

                <div className="p-4">
                  <div className="mb-2">
                    <span className="inline-block bg-hb-pink-light text-hb-pink text-xs px-2 py-1 rounded-full">
                      {decodeHTMLEntities(article._embedded["wp:term"][0][0].name)}
                    </span>
                  </div>

                  <h3 className="text-xl font-bold mb-3 line-clamp-2">
                    {article.title.rendered}
                  </h3>

                  <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                    {article.excerpt.rendered.replace(/<[^>]*>/g, '').slice(0, 150)}...
                  </p>

                  <div className="flex items-center justify-between">
                    <div className="text-xs text-gray-500">
                      {new Date(article.date).toLocaleDateString('id-ID', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </div>

                    <Link
                      href={`/blog/${article.slug}`}
                      className="text-hb-pink text-sm font-medium flex items-center gap-2 hover:gap-3 transition-all"
                    >
                      Baca Selengkapnya
                      <Icon icon="guidance:left-arrow" className="text-lg" />
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {hasMore && !loading && (
            <div className="text-center mt-8">
              <button
                onClick={loadMore}
                className="btn-primary"
              >
                Muat Lebih Banyak
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Add CSS for line-clamp
const styles = `
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
`;

if (typeof document !== 'undefined') {
  const styleSheet = document.createElement("style");
  styleSheet.innerText = styles;
  document.head.appendChild(styleSheet);
}
