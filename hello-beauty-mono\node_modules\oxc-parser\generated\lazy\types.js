// Auto-generated code, DO NOT EDIT DIRECTLY!
// To edit this generated file you have to edit `tasks/ast_tools/src/generators/raw_transfer_lazy.rs`.

'use strict';

// Mapping from node type name to node type ID
const NODE_TYPE_IDS_MAP = new Map([
  // Leaf nodes
  ['IdentifierName', 0],
  ['IdentifierReference', 1],
  ['BindingIdentifier', 2],
  ['LabelIdentifier', 3],
  ['ThisExpression', 4],
  ['Elision', 5],
  ['TemplateElement', 6],
  ['Super', 7],
  ['Hashbang', 8],
  ['EmptyStatement', 9],
  ['DebuggerStatement', 10],
  ['PrivateIdentifier', 11],
  ['BooleanLiteral', 12],
  ['NullLiteral', 13],
  ['NumericLiteral', 14],
  ['StringLiteral', 15],
  ['BigIntLiteral', 16],
  ['RegExpLiteral', 17],
  ['JSXOpeningFragment', 18],
  ['JSXClosingFragment', 19],
  ['JSXEmptyExpression', 20],
  ['JSXIdentifier', 21],
  ['JSXText', 22],
  ['TSAnyKeyword', 23],
  ['TSStringKeyword', 24],
  ['TSBooleanKeyword', 25],
  ['TSNumberKeyword', 26],
  ['TSNeverKeyword', 27],
  ['TSIntrinsicKeyword', 28],
  ['TSUnknownKeyword', 29],
  ['TSNullKeyword', 30],
  ['TSUndefinedKeyword', 31],
  ['TSVoidKeyword', 32],
  ['TSSymbolKeyword', 33],
  ['TSThisType', 34],
  ['TSObjectKeyword', 35],
  ['TSBigIntKeyword', 36],
  ['JSDocUnknownType', 37],
  // Non-leaf nodes
  ['Program', 38],
  ['ArrayExpression', 39],
  ['ObjectExpression', 40],
  ['ObjectProperty', 41],
  ['TemplateLiteral', 42],
  ['TaggedTemplateExpression', 43],
  ['ComputedMemberExpression', 44],
  ['StaticMemberExpression', 45],
  ['PrivateFieldExpression', 46],
  ['CallExpression', 47],
  ['NewExpression', 48],
  ['MetaProperty', 49],
  ['SpreadElement', 50],
  ['UpdateExpression', 51],
  ['UnaryExpression', 52],
  ['BinaryExpression', 53],
  ['PrivateInExpression', 54],
  ['LogicalExpression', 55],
  ['ConditionalExpression', 56],
  ['AssignmentExpression', 57],
  ['ArrayAssignmentTarget', 58],
  ['ObjectAssignmentTarget', 59],
  ['AssignmentTargetWithDefault', 60],
  ['AssignmentTargetPropertyIdentifier', 61],
  ['AssignmentTargetPropertyProperty', 62],
  ['SequenceExpression', 63],
  ['AwaitExpression', 64],
  ['ChainExpression', 65],
  ['ParenthesizedExpression', 66],
  ['BlockStatement', 67],
  ['VariableDeclaration', 68],
  ['VariableDeclarator', 69],
  ['ExpressionStatement', 70],
  ['IfStatement', 71],
  ['DoWhileStatement', 72],
  ['WhileStatement', 73],
  ['ForStatement', 74],
  ['ForInStatement', 75],
  ['ForOfStatement', 76],
  ['ContinueStatement', 77],
  ['BreakStatement', 78],
  ['ReturnStatement', 79],
  ['WithStatement', 80],
  ['SwitchStatement', 81],
  ['SwitchCase', 82],
  ['LabeledStatement', 83],
  ['ThrowStatement', 84],
  ['TryStatement', 85],
  ['CatchClause', 86],
  ['AssignmentPattern', 87],
  ['ObjectPattern', 88],
  ['BindingProperty', 89],
  ['ArrayPattern', 90],
  ['Function', 91],
  ['FormalParameters', 92],
  ['FunctionBody', 93],
  ['ArrowFunctionExpression', 94],
  ['YieldExpression', 95],
  ['Class', 96],
  ['ClassBody', 97],
  ['MethodDefinition', 98],
  ['PropertyDefinition', 99],
  ['StaticBlock', 100],
  ['AccessorProperty', 101],
  ['ImportExpression', 102],
  ['ImportDeclaration', 103],
  ['ImportSpecifier', 104],
  ['ImportDefaultSpecifier', 105],
  ['ImportNamespaceSpecifier', 106],
  ['ImportAttribute', 107],
  ['ExportNamedDeclaration', 108],
  ['ExportDefaultDeclaration', 109],
  ['ExportAllDeclaration', 110],
  ['ExportSpecifier', 111],
  ['V8IntrinsicExpression', 112],
  ['JSXElement', 113],
  ['JSXOpeningElement', 114],
  ['JSXClosingElement', 115],
  ['JSXFragment', 116],
  ['JSXNamespacedName', 117],
  ['JSXMemberExpression', 118],
  ['JSXExpressionContainer', 119],
  ['JSXAttribute', 120],
  ['JSXSpreadAttribute', 121],
  ['JSXSpreadChild', 122],
  ['TSEnumDeclaration', 123],
  ['TSEnumBody', 124],
  ['TSEnumMember', 125],
  ['TSTypeAnnotation', 126],
  ['TSLiteralType', 127],
  ['TSConditionalType', 128],
  ['TSUnionType', 129],
  ['TSIntersectionType', 130],
  ['TSParenthesizedType', 131],
  ['TSTypeOperator', 132],
  ['TSArrayType', 133],
  ['TSIndexedAccessType', 134],
  ['TSTupleType', 135],
  ['TSNamedTupleMember', 136],
  ['TSOptionalType', 137],
  ['TSRestType', 138],
  ['TSTypeReference', 139],
  ['TSQualifiedName', 140],
  ['TSTypeParameterInstantiation', 141],
  ['TSTypeParameter', 142],
  ['TSTypeParameterDeclaration', 143],
  ['TSTypeAliasDeclaration', 144],
  ['TSClassImplements', 145],
  ['TSInterfaceDeclaration', 146],
  ['TSInterfaceBody', 147],
  ['TSPropertySignature', 148],
  ['TSIndexSignature', 149],
  ['TSCallSignatureDeclaration', 150],
  ['TSMethodSignature', 151],
  ['TSConstructSignatureDeclaration', 152],
  ['TSIndexSignatureName', 153],
  ['TSInterfaceHeritage', 154],
  ['TSTypePredicate', 155],
  ['TSModuleDeclaration', 156],
  ['TSModuleBlock', 157],
  ['TSTypeLiteral', 158],
  ['TSInferType', 159],
  ['TSTypeQuery', 160],
  ['TSImportType', 161],
  ['TSFunctionType', 162],
  ['TSConstructorType', 163],
  ['TSMappedType', 164],
  ['TSTemplateLiteralType', 165],
  ['TSAsExpression', 166],
  ['TSSatisfiesExpression', 167],
  ['TSTypeAssertion', 168],
  ['TSImportEqualsDeclaration', 169],
  ['TSExternalModuleReference', 170],
  ['TSNonNullExpression', 171],
  ['Decorator', 172],
  ['TSExportAssignment', 173],
  ['TSNamespaceExportDeclaration', 174],
  ['TSInstantiationExpression', 175],
  ['JSDocNullableType', 176],
  ['JSDocNonNullableType', 177],
]);

module.exports = {
  NODE_TYPE_IDS_MAP,
  NODE_TYPES_COUNT: 178,
  LEAF_NODE_TYPES_COUNT: 38,
};
