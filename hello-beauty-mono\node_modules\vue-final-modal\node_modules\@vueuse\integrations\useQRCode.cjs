'use strict';

var shared = require('@vueuse/shared');
var vueDemi = require('vue-demi');
var QRCode = require('qrcode');

function useQRCode(text, options) {
  const src = shared.toRef(text);
  const result = vueDemi.ref("");
  vueDemi.watch(
    src,
    async (value) => {
      if (src.value && shared.isClient)
        result.value = await QRCode.toDataURL(value, options);
    },
    { immediate: true }
  );
  return result;
}

exports.useQRCode = useQRCode;
