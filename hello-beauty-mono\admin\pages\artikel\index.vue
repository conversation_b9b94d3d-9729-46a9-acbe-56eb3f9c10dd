<template>
  <div class="p-3">
    <div class="bg-white rounded-lg border">
      <div class="px-4 pt-4 flex justify-between items-center">
        <h1 class="font-bold text-xl">Artikel</h1>
        <button
          class="btn-primary flex items-center"
          @click.prevent="$router.push('/artikel/create')"
        >
          <icon name="jam:plus" class="text-xl" />
          Tambah Artikel
        </button>
      </div>
      
      <!-- Filters -->
      <div class="grid grid-cols-2 md:grid-cols-4 p-3 gap-2 mt-4">
        <select
          v-model="search.status"
          class="p-2 border border-gray-200 rounded-lg"
          @change="getData()"
        >
          <option value="">Semua Status</option>
          <option value="draft">Draft</option>
          <option value="published">Published</option>
          <option value="archived">Archived</option>
        </select>
        
        <select
          v-model="search.category"
          class="p-2 border border-gray-200 rounded-lg"
          @change="getData()"
        >
          <option value="">Se<PERSON><PERSON></option>
          <option v-for="cat in categories" :key="cat" :value="cat">{{ cat }}</option>
        </select>
        
        <input
          v-model="search.keyword"
          type="text"
          class="p-2 border border-gray-200 rounded-lg"
          placeholder="Cari artikel..."
          @keyup.enter="getData()"
        />
        
        <button
          class="btn-secondary"
          @click.prevent="resetSearch()"
        >
          Reset
        </button>
      </div>

      <!-- Table -->
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Artikel
              </th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Kategori
              </th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Views
              </th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Tanggal
              </th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Aksi
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-if="loading">
              <td colspan="6" class="px-4 py-8 text-center">
                <div class="flex justify-center">
                  <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              </td>
            </tr>
            <tr v-else-if="articles.length === 0">
              <td colspan="6" class="px-4 py-8 text-center text-gray-500">
                Tidak ada artikel ditemukan
              </td>
            </tr>
            <tr v-else v-for="article in articles" :key="article._id" class="hover:bg-gray-50">
              <td class="px-4 py-4">
                <div class="flex items-center">
                  <img
                    v-if="article.featuredImageUrl"
                    :src="article.featuredImageUrl"
                    :alt="article.title"
                    class="h-12 w-12 rounded-lg object-cover mr-3"
                  />
                  <div class="h-12 w-12 bg-gray-200 rounded-lg mr-3 flex items-center justify-center" v-else>
                    <icon name="tabler:photo" class="text-gray-400" />
                  </div>
                  <div>
                    <div class="text-sm font-medium text-gray-900 line-clamp-2">
                      {{ article.title }}
                    </div>
                    <div class="text-sm text-gray-500 line-clamp-1">
                      {{ article.excerpt }}
                    </div>
                  </div>
                </div>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {{ article.category }}
                </span>
              </td>
              <td class="px-4 py-4 whitespace-nowrap">
                <span
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="{
                    'bg-green-100 text-green-800': article.status === 'published',
                    'bg-yellow-100 text-yellow-800': article.status === 'draft',
                    'bg-gray-100 text-gray-800': article.status === 'archived'
                  }"
                >
                  {{ article.status }}
                </span>
              </td>
              <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ article.viewCount || 0 }}
              </td>
              <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ formatDate(article.publishedAt || article.createdAt) }}
              </td>
              <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-2">
                  <button
                    @click="$router.push(`/artikel/edit/${article._id}`)"
                    class="text-indigo-600 hover:text-indigo-900"
                  >
                    <icon name="tabler:edit" />
                  </button>
                  <button
                    @click="toggleStatus(article)"
                    class="text-blue-600 hover:text-blue-900"
                    :title="article.isActive ? 'Nonaktifkan' : 'Aktifkan'"
                  >
                    <icon :name="article.isActive ? 'tabler:eye' : 'tabler:eye-off'" />
                  </button>
                  <button
                    @click="deleteArticle(article)"
                    class="text-red-600 hover:text-red-900"
                  >
                    <icon name="tabler:trash" />
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="p-3">
        <div class="flex justify-between items-center">
          <div class="text-sm text-gray-700">
            Menampilkan {{ ((page - 1) * limit) + 1 }} - {{ Math.min(page * limit, total) }} dari {{ total }} artikel
          </div>
          <div class="flex justify-end gap-2 items-center">
            <button
              v-if="page > 1"
              class="btn-secondary"
              @click.prevent="page--, getData()"
            >
              Prev
            </button>
            <form @submit.prevent="getData()">
              <input
                v-model="page"
                class="text-sm py-2 w-16 text-center border rounded-lg"
                type="number"
                min="1"
                :max="totalPages"
              />
            </form>
            <span class="text-sm text-gray-500">dari {{ totalPages }}</span>
            <button
              v-if="page < totalPages"
              class="btn-secondary"
              @click.prevent="page++, getData()"
            >
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  middleware: "auth-admin",
});

useHead({
  title: "Artikel",
  meta: [
    {
      name: "description",
      content: "Manajemen Artikel",
    },
  ],
});

const articles = ref([]);
const categories = ref([]);
const loading = ref(false);
const page = ref(1);
const limit = ref(10);
const total = ref(0);
const totalPages = ref(0);

const search = ref({
  status: '',
  category: '',
  keyword: ''
});

const getData = async () => {
  try {
    loading.value = true;
    const params = new URLSearchParams({
      page: page.value,
      limit: limit.value,
      ...(search.value.status && { status: search.value.status }),
      ...(search.value.category && { category: search.value.category }),
      ...(search.value.keyword && { search: search.value.keyword })
    });

    const api = useAdminApi();
    const data = await api.get(`/chio/article?${params}`);

    articles.value = data.data;
    total.value = data.pagination.totalItems;
    totalPages.value = data.pagination.totalPages;
  } catch (error) {
    console.error('Error fetching articles:', error);
    useNuxtApp().$toast.error('Gagal memuat artikel');
  } finally {
    loading.value = false;
  }
};

const getCategories = async () => {
  try {
    const api = useAdminApi();
    const data = await api.get('/chio/article');

    const uniqueCategories = [...new Set(data.data.map(article => article.category))];
    categories.value = uniqueCategories;
  } catch (error) {
    console.error('Error fetching categories:', error);
  }
};

const resetSearch = () => {
  search.value = {
    status: '',
    category: '',
    keyword: ''
  };
  page.value = 1;
  getData();
};

const toggleStatus = async (article) => {
  try {
    const api = useAdminApi();
    await api.patch(`/chio/article/${article._id}/toggle`);

    useNuxtApp().$toast.success(`Artikel ${article.isActive ? 'dinonaktifkan' : 'diaktifkan'}`);
    getData();
  } catch (error) {
    console.error('Error toggling status:', error);
    useNuxtApp().$toast.error('Gagal mengubah status artikel');
  }
};

const deleteArticle = async (article) => {
  if (!confirm(`Apakah Anda yakin ingin menghapus artikel "${article.title}"?`)) {
    return;
  }

  try {
    const api = useAdminApi();
    await api.delete(`/chio/article/${article._id}`);

    useNuxtApp().$toast.success('Artikel berhasil dihapus');
    getData();
  } catch (error) {
    console.error('Error deleting article:', error);
    useNuxtApp().$toast.error('Gagal menghapus artikel');
  }
};

const formatDate = (date) => {
  if (!date) return '-';
  return new Date(date).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

onMounted(() => {
  getData();
  getCategories();
});
</script>

<style scoped>
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
