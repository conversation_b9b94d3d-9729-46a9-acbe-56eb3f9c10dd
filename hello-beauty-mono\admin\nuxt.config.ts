// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  app: {
    head: {
      title: 'HelloBeauty',
      charset: 'utf-8',
      viewport: 'width=device-width, initial-scale=1',
      script: [],
      link: [
        { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
        { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: '' },
        { rel: 'stylesheet', href: 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap' },
        { rel: 'icon', type: 'image/png', href: '/favicon.png' },
      ],
    },
    pageTransition: { name: 'page', mode: 'out-in' },
  },

  ssr: true,

  css: [
    '/assets/css/main.css',
    '/assets/css/final-modal.css',
    '~/assets/css/select2.css'
  ],

  modules: [
    '@pinia/nuxt',
    'nuxt-icon',
    '@nuxt/eslint',
    '@nuxtjs/tailwindcss',
  ],

  plugins: [],

  components: [
    {
      path: '~/components',
      pathPrefix: false,
    },
  ],

  nitro: {
    esbuild: {
      options: {
        target: 'esnext',
      },
    },
  },

  vite: {
    define: {
      global: 'globalThis',
    },
    optimizeDeps: {
      exclude: ['form-data', 'axios']
    }
  },

   runtimeConfig: {
    public: {
      API_URL: process.env.API_URL,
    },
  },

  postcss: {
    plugins: {
      tailwindcss: {},
      autoprefixer: {},
    },
  },

  imports: {
    dirs: [
      'composables',
      'composables/*/index.{ts,js,mjs,mts}',
      'composables/**',
    ],
  },

  devtools: {
    enabled: true,
  },

  // Pinia configuration
  pinia: {
    autoImports: ['defineStore', 'acceptHMRUpdate'],
  },

  // Build configuration
  build: {
    transpile: ['pinia'],
  },

  // Vite configuration
  vite: {
    optimizeDeps: {
      include: ['pinia'],
    },
  },

  compatibilityDate: '2024-09-13',
});
