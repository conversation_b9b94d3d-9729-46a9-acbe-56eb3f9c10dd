{"version": 3, "sources": ["../../../src/checkers/eslint/main.ts"], "sourcesContent": ["import Module from 'node:module'\nimport path from 'node:path'\nimport { fileURLToPath } from 'node:url'\nimport { parentPort } from 'node:worker_threads'\nimport chokidar from 'chokidar'\nimport { ESLint } from 'eslint'\nimport invariant from 'tiny-invariant'\n\nimport { Checker } from '../../Checker.js'\nimport { FileDiagnosticManager } from '../../FileDiagnosticManager.js'\nimport { createIgnore } from '../../glob.js'\nimport {\n  composeCheckerSummary,\n  consoleLog,\n  diagnosticToConsoleLevel,\n  diagnosticToRuntimeError,\n  diagnosticToTerminalLog,\n  filterLogLevel,\n  normalizeEslintDiagnostic,\n  toClientPayload,\n} from '../../logger.js'\nimport { ACTION_TYPES, DiagnosticLevel } from '../../types.js'\nimport { translateOptions } from './cli.js'\nimport { options as optionator } from './options.js'\n\nconst __filename = fileURLToPath(import.meta.url)\nconst require = Module.createRequire(import.meta.url)\n\nconst manager = new FileDiagnosticManager()\nlet createServeAndBuild: any\n\nimport type { CreateDiagnostic } from '../../types'\n\nconst createDiagnostic: CreateDiagnostic<'eslint'> = (pluginConfig) => {\n  let overlay = true\n  let terminal = true\n\n  return {\n    config: async ({ enableOverlay, enableTerminal }) => {\n      overlay = enableOverlay\n      terminal = enableTerminal\n    },\n    async configureServer({ root }) {\n      if (!pluginConfig.eslint) return\n\n      const options = optionator.parse(pluginConfig.eslint.lintCommand)\n      invariant(\n        !options.fix,\n        'Using `--fix` in `config.eslint.lintCommand` is not allowed in vite-plugin-checker, you could using `--fix` with editor.',\n      )\n\n      const translatedOptions = translateOptions(options) as ESLint.Options\n\n      const logLevel = (() => {\n        if (typeof pluginConfig.eslint !== 'object') return undefined\n        const userLogLevel = pluginConfig.eslint.dev?.logLevel\n        if (!userLogLevel) return undefined\n        const map = {\n          error: DiagnosticLevel.Error,\n          warning: DiagnosticLevel.Warning,\n        } as const\n\n        return userLogLevel.map((l) => map[l])\n      })()\n\n      const eslintOptions: ESLint.Options = {\n        cwd: root,\n        ...translatedOptions,\n        ...pluginConfig.eslint.dev?.overrideConfig,\n      }\n\n      let eslint: ESLint\n      if (pluginConfig.eslint.useFlatConfig) {\n        const {\n          FlatESLint,\n          shouldUseFlatConfig,\n        } = require('eslint/use-at-your-own-risk')\n        if (shouldUseFlatConfig?.()) {\n          eslint = new FlatESLint({\n            cwd: root,\n          })\n        } else {\n          throw Error(\n            'Please upgrade your eslint to latest version to use `useFlatConfig` option.',\n          )\n        }\n      } else {\n        eslint = new ESLint(eslintOptions)\n      }\n\n      const dispatchDiagnostics = () => {\n        const diagnostics = filterLogLevel(manager.getDiagnostics(), logLevel)\n        if (terminal) {\n          for (const d of diagnostics) {\n            consoleLog(\n              diagnosticToTerminalLog(d, 'ESLint'),\n              diagnosticToConsoleLevel(d),\n            )\n          }\n\n          const errorCount = diagnostics.filter(\n            (d) => d.level === DiagnosticLevel.Error,\n          ).length\n          const warningCount = diagnostics.filter(\n            (d) => d.level === DiagnosticLevel.Warning,\n          ).length\n          consoleLog(\n            composeCheckerSummary('ESLint', errorCount, warningCount),\n            errorCount ? 'error' : warningCount ? 'warn' : 'info',\n          )\n        }\n\n        if (overlay) {\n          parentPort?.postMessage({\n            type: ACTION_TYPES.overlayError,\n            payload: toClientPayload(\n              'eslint',\n              diagnostics.map((d) => diagnosticToRuntimeError(d)),\n            ),\n          })\n        }\n      }\n\n      const handleFileChange = async (\n        filePath: string,\n        type: 'change' | 'unlink',\n      ) => {\n        // See: https://github.com/eslint/eslint/pull/4465\n        const extension = path.extname(filePath)\n        const { extensions } = eslintOptions\n        const hasExtensionsConfig = Array.isArray(extensions)\n        if (hasExtensionsConfig && !extensions.includes(extension)) return\n\n        const isChangedFileIgnored = await eslint.isPathIgnored(filePath)\n        if (isChangedFileIgnored) return\n\n        const absPath = path.resolve(root, filePath)\n        if (type === 'unlink') {\n          manager.updateByFileId(absPath, [])\n        } else if (type === 'change') {\n          const diagnosticsOfChangedFile = await eslint.lintFiles(filePath)\n          const newDiagnostics = diagnosticsOfChangedFile.flatMap((d) =>\n            normalizeEslintDiagnostic(d),\n          )\n          manager.updateByFileId(absPath, newDiagnostics)\n        }\n\n        dispatchDiagnostics()\n      }\n\n      // initial lint\n      const files = options._.slice(1) as string[]\n      const diagnostics = await eslint.lintFiles(files)\n\n      manager.initWith(diagnostics.flatMap((p) => normalizeEslintDiagnostic(p)))\n      dispatchDiagnostics()\n\n      // watch lint\n      let watchTarget: string | string[] = root\n      if (pluginConfig.eslint.watchPath) {\n        if (Array.isArray(pluginConfig.eslint.watchPath)) {\n          watchTarget = pluginConfig.eslint.watchPath.map((p) =>\n            path.resolve(root, p),\n          )\n        } else {\n          watchTarget = path.resolve(root, pluginConfig.eslint.watchPath)\n        }\n      }\n\n      const watcher = chokidar.watch(watchTarget, {\n        cwd: root,\n        ignored: createIgnore(root, files),\n      })\n\n      watcher.on('change', async (filePath) => {\n        handleFileChange(filePath, 'change')\n      })\n      watcher.on('unlink', async (filePath) => {\n        handleFileChange(filePath, 'unlink')\n      })\n    },\n  }\n}\n\nexport class EslintChecker extends Checker<'eslint'> {\n  public constructor() {\n    super({\n      name: 'eslint',\n      absFilePath: __filename,\n      build: {\n        buildBin: (pluginConfig) => {\n          if (pluginConfig.eslint) {\n            const { lintCommand } = pluginConfig.eslint\n            return ['eslint', lintCommand.split(' ').slice(1)]\n          }\n          return ['eslint', ['']]\n        },\n      },\n      createDiagnostic,\n    })\n  }\n\n  public init() {\n    const _createServeAndBuild = super.initMainThread()\n    createServeAndBuild = _createServeAndBuild\n    super.initWorkerThread()\n  }\n}\n\nexport { createServeAndBuild }\nconst eslintChecker = new EslintChecker()\neslintChecker.prepare()\neslintChecker.init()\n"], "mappings": "AAAA,OAAO,YAAY;AACnB,OAAO,UAAU;AACjB,SAAS,qBAAqB;AAC9B,SAAS,kBAAkB;AAC3B,OAAO,cAAc;AACrB,SAAS,cAAc;AACvB,OAAO,eAAe;AAEtB,SAAS,eAAe;AACxB,SAAS,6BAA6B;AACtC,SAAS,oBAAoB;AAC7B;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP,SAAS,cAAc,uBAAuB;AAC9C,SAAS,wBAAwB;AACjC,SAAS,WAAW,kBAAkB;AAEtC,MAAM,aAAa,cAAc,YAAY,GAAG;AAChD,MAAMA,WAAU,OAAO,cAAc,YAAY,GAAG;AAEpD,MAAM,UAAU,IAAI,sBAAsB;AAC1C,IAAI;AAIJ,MAAM,mBAA+C,CAAC,iBAAiB;AACrE,MAAI,UAAU;AACd,MAAI,WAAW;AAEf,SAAO;AAAA,IACL,QAAQ,OAAO,EAAE,eAAe,eAAe,MAAM;AACnD,gBAAU;AACV,iBAAW;AAAA,IACb;AAAA,IACA,MAAM,gBAAgB,EAAE,KAAK,GAAG;AA1CpC;AA2CM,UAAI,CAAC,aAAa,OAAQ;AAE1B,YAAM,UAAU,WAAW,MAAM,aAAa,OAAO,WAAW;AAChE;AAAA,QACE,CAAC,QAAQ;AAAA,QACT;AAAA,MACF;AAEA,YAAM,oBAAoB,iBAAiB,OAAO;AAElD,YAAM,YAAY,MAAM;AArD9B,YAAAC;AAsDQ,YAAI,OAAO,aAAa,WAAW,SAAU,QAAO;AACpD,cAAM,gBAAeA,MAAA,aAAa,OAAO,QAApB,gBAAAA,IAAyB;AAC9C,YAAI,CAAC,aAAc,QAAO;AAC1B,cAAM,MAAM;AAAA,UACV,OAAO,gBAAgB;AAAA,UACvB,SAAS,gBAAgB;AAAA,QAC3B;AAEA,eAAO,aAAa,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC;AAAA,MACvC,GAAG;AAEH,YAAM,gBAAgC;AAAA,QACpC,KAAK;AAAA,QACL,GAAG;AAAA,QACH,IAAG,kBAAa,OAAO,QAApB,mBAAyB;AAAA,MAC9B;AAEA,UAAI;AACJ,UAAI,aAAa,OAAO,eAAe;AACrC,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAID,SAAQ,6BAA6B;AACzC,YAAI,8DAAyB;AAC3B,mBAAS,IAAI,WAAW;AAAA,YACtB,KAAK;AAAA,UACP,CAAC;AAAA,QACH,OAAO;AACL,gBAAM;AAAA,YACJ;AAAA,UACF;AAAA,QACF;AAAA,MACF,OAAO;AACL,iBAAS,IAAI,OAAO,aAAa;AAAA,MACnC;AAEA,YAAM,sBAAsB,MAAM;AA1FxC,YAAAC;AA2FQ,cAAMC,eAAc,eAAe,QAAQ,eAAe,GAAG,QAAQ;AACrE,YAAI,UAAU;AACZ,qBAAW,KAAKA,cAAa;AAC3B;AAAA,cACE,wBAAwB,GAAG,QAAQ;AAAA,cACnC,yBAAyB,CAAC;AAAA,YAC5B;AAAA,UACF;AAEA,gBAAM,aAAaA,aAAY;AAAA,YAC7B,CAAC,MAAM,EAAE,UAAU,gBAAgB;AAAA,UACrC,EAAE;AACF,gBAAM,eAAeA,aAAY;AAAA,YAC/B,CAAC,MAAM,EAAE,UAAU,gBAAgB;AAAA,UACrC,EAAE;AACF;AAAA,YACE,sBAAsB,UAAU,YAAY,YAAY;AAAA,YACxD,aAAa,UAAU,eAAe,SAAS;AAAA,UACjD;AAAA,QACF;AAEA,YAAI,SAAS;AACX,WAAAD,MAAA,+BAAAA,IAAY,YAAY;AAAA,YACtB,MAAM,aAAa;AAAA,YACnB,SAAS;AAAA,cACP;AAAA,cACAC,aAAY,IAAI,CAAC,MAAM,yBAAyB,CAAC,CAAC;AAAA,YACpD;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,YAAM,mBAAmB,OACvB,UACA,SACG;AAEH,cAAM,YAAY,KAAK,QAAQ,QAAQ;AACvC,cAAM,EAAE,WAAW,IAAI;AACvB,cAAM,sBAAsB,MAAM,QAAQ,UAAU;AACpD,YAAI,uBAAuB,CAAC,WAAW,SAAS,SAAS,EAAG;AAE5D,cAAM,uBAAuB,MAAM,OAAO,cAAc,QAAQ;AAChE,YAAI,qBAAsB;AAE1B,cAAM,UAAU,KAAK,QAAQ,MAAM,QAAQ;AAC3C,YAAI,SAAS,UAAU;AACrB,kBAAQ,eAAe,SAAS,CAAC,CAAC;AAAA,QACpC,WAAW,SAAS,UAAU;AAC5B,gBAAM,2BAA2B,MAAM,OAAO,UAAU,QAAQ;AAChE,gBAAM,iBAAiB,yBAAyB;AAAA,YAAQ,CAAC,MACvD,0BAA0B,CAAC;AAAA,UAC7B;AACA,kBAAQ,eAAe,SAAS,cAAc;AAAA,QAChD;AAEA,4BAAoB;AAAA,MACtB;AAGA,YAAM,QAAQ,QAAQ,EAAE,MAAM,CAAC;AAC/B,YAAM,cAAc,MAAM,OAAO,UAAU,KAAK;AAEhD,cAAQ,SAAS,YAAY,QAAQ,CAAC,MAAM,0BAA0B,CAAC,CAAC,CAAC;AACzE,0BAAoB;AAGpB,UAAI,cAAiC;AACrC,UAAI,aAAa,OAAO,WAAW;AACjC,YAAI,MAAM,QAAQ,aAAa,OAAO,SAAS,GAAG;AAChD,wBAAc,aAAa,OAAO,UAAU;AAAA,YAAI,CAAC,MAC/C,KAAK,QAAQ,MAAM,CAAC;AAAA,UACtB;AAAA,QACF,OAAO;AACL,wBAAc,KAAK,QAAQ,MAAM,aAAa,OAAO,SAAS;AAAA,QAChE;AAAA,MACF;AAEA,YAAM,UAAU,SAAS,MAAM,aAAa;AAAA,QAC1C,KAAK;AAAA,QACL,SAAS,aAAa,MAAM,KAAK;AAAA,MACnC,CAAC;AAED,cAAQ,GAAG,UAAU,OAAO,aAAa;AACvC,yBAAiB,UAAU,QAAQ;AAAA,MACrC,CAAC;AACD,cAAQ,GAAG,UAAU,OAAO,aAAa;AACvC,yBAAiB,UAAU,QAAQ;AAAA,MACrC,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAEO,MAAM,sBAAsB,QAAkB;AAAA,EAC5C,cAAc;AACnB,UAAM;AAAA,MACJ,MAAM;AAAA,MACN,aAAa;AAAA,MACb,OAAO;AAAA,QACL,UAAU,CAAC,iBAAiB;AAC1B,cAAI,aAAa,QAAQ;AACvB,kBAAM,EAAE,YAAY,IAAI,aAAa;AACrC,mBAAO,CAAC,UAAU,YAAY,MAAM,GAAG,EAAE,MAAM,CAAC,CAAC;AAAA,UACnD;AACA,iBAAO,CAAC,UAAU,CAAC,EAAE,CAAC;AAAA,QACxB;AAAA,MACF;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEO,OAAO;AACZ,UAAM,uBAAuB,MAAM,eAAe;AAClD,0BAAsB;AACtB,UAAM,iBAAiB;AAAA,EACzB;AACF;AAGA,MAAM,gBAAgB,IAAI,cAAc;AACxC,cAAc,QAAQ;AACtB,cAAc,KAAK;", "names": ["require", "_a", "diagnostics"]}