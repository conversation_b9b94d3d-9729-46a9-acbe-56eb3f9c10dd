// Generated by dts-bundle-generator v9.5.1

type Node$1 = DocumentNode | ElementNode | TextNode | CommentNode | DoctypeNode;
type NodeType = typeof DOCUMENT_NODE | typeof ELEMENT_NODE | typeof TEXT_NODE | typeof COMMENT_NODE | typeof DOCTYPE_NODE;
interface Location$1 {
	start: number;
	end: number;
}
interface BaseNode {
	type: NodeType;
	loc: [
		Location$1,
		Location$1
	];
	parent: Node$1;
	[key: string]: any;
}
interface LiteralNode extends BaseNode {
	value: string;
}
interface ParentNode$1 extends BaseNode {
	children: Node$1[];
}
interface DocumentNode extends Omit<ParentNode$1, "parent"> {
	type: typeof DOCUMENT_NODE;
	attributes: Record<string, string>;
	parent: undefined;
}
interface ElementNode extends ParentNode$1 {
	type: typeof ELEMENT_NODE;
	name: string;
	attributes: Record<string, string>;
}
interface TextNode extends LiteralNode {
	type: typeof TEXT_NODE;
}
interface CommentNode extends LiteralNode {
	type: typeof COMMENT_NODE;
}
interface DoctypeNode extends LiteralNode {
	type: typeof DOCTYPE_NODE;
}
declare const DOCUMENT_NODE = 0;
declare const ELEMENT_NODE = 1;
declare const TEXT_NODE = 2;
declare const COMMENT_NODE = 3;
declare const DOCTYPE_NODE = 4;
export declare const Fragment: unique symbol;
declare function createVNode(type: any, { children, ...attributes }: Record<string, any>, key: string, __self: string, __source: string): ElementNode;

export {
	createVNode as jsx,
	createVNode as jsxDEV,
	createVNode as jsxs,
};

export {};
