{"name": "@whatwg-node/disposablestack", "version": "0.0.6", "description": "Cross Platform Smart DisposableStack API Ponyfill", "sideEffects": false, "dependencies": {"@whatwg-node/promise-helpers": "^1.0.0", "tslib": "^2.6.3"}, "repository": {"type": "git", "url": "ardatan/whatwg-node", "directory": "packages/disposablestack"}, "author": "Arda TANRIKULU <<EMAIL>>", "license": "MIT", "engines": {"node": ">=18.0.0"}, "main": "cjs/index.js", "module": "esm/index.js", "typings": "typings/index.d.ts", "typescript": {"definition": "typings/index.d.ts"}, "type": "module", "exports": {".": {"require": {"types": "./typings/index.d.cts", "default": "./cjs/index.js"}, "import": {"types": "./typings/index.d.ts", "default": "./esm/index.js"}, "default": {"types": "./typings/index.d.ts", "default": "./esm/index.js"}}, "./package.json": "./package.json"}}