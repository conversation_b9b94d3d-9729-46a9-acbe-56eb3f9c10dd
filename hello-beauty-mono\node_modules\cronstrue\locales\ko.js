(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory(require("cronstrue"));
	else if(typeof define === 'function' && define.amd)
		define("locales/ko", ["cronstrue"], factory);
	else if(typeof exports === 'object')
		exports["locales/ko"] = factory(require("cronstrue"));
	else
		root["locales/ko"] = factory(root["cronstrue"]);
})(globalThis, (__WEBPACK_EXTERNAL_MODULE__93__) => {
return /******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ 93:
/***/ ((module) => {

module.exports = __WEBPACK_EXTERNAL_MODULE__93__;

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var cronstrue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(93);
/* harmony import */ var cronstrue__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(cronstrue__WEBPACK_IMPORTED_MODULE_0__);
var exports = __webpack_exports__;
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ko = void 0;
var ko = (function () {
    function ko() {
    }
    ko.prototype.setPeriodBeforeTime = function () {
        return true;
    };
    ko.prototype.pm = function () {
        return "오후";
    };
    ko.prototype.am = function () {
        return "오전";
    };
    ko.prototype.atX0SecondsPastTheMinuteGt20 = function () {
        return null;
    };
    ko.prototype.atX0MinutesPastTheHourGt20 = function () {
        return null;
    };
    ko.prototype.commaMonthX0ThroughMonthX1 = function () {
        return null;
    };
    ko.prototype.commaYearX0ThroughYearX1 = function () {
        return null;
    };
    ko.prototype.use24HourTimeFormatByDefault = function () {
        return false;
    };
    ko.prototype.anErrorOccuredWhenGeneratingTheExpressionD = function () {
        return "표현식 설명을 생성하는 중 오류가 발생했습니다. cron 표현식 구문을 확인하십시오.";
    };
    ko.prototype.everyMinute = function () {
        return "1분마다";
    };
    ko.prototype.everyHour = function () {
        return "1시간마다";
    };
    ko.prototype.atSpace = function () {
        return "시간 ";
    };
    ko.prototype.everyMinuteBetweenX0AndX1 = function () {
        return "%s 및 %s 사이에 매 분";
    };
    ko.prototype.at = function () {
        return "시간";
    };
    ko.prototype.spaceAnd = function () {
        return " 및";
    };
    ko.prototype.everySecond = function () {
        return "1초마다";
    };
    ko.prototype.everyX0Seconds = function () {
        return "%s초마다";
    };
    ko.prototype.secondsX0ThroughX1PastTheMinute = function () {
        return "정분 후 %s초에서 %s초까지";
    };
    ko.prototype.atX0SecondsPastTheMinute = function () {
        return "정분 후 %s초에서";
    };
    ko.prototype.everyX0Minutes = function () {
        return "%s분마다";
    };
    ko.prototype.minutesX0ThroughX1PastTheHour = function () {
        return "정시 후 %s분에서 %s분까지";
    };
    ko.prototype.atX0MinutesPastTheHour = function () {
        return "정시 후 %s분에서";
    };
    ko.prototype.everyX0Hours = function () {
        return "%s시간마다";
    };
    ko.prototype.betweenX0AndX1 = function () {
        return "%s에서 %s 사이";
    };
    ko.prototype.atX0 = function () {
        return "%s에서";
    };
    ko.prototype.commaEveryDay = function () {
        return ", 매일";
    };
    ko.prototype.commaEveryX0DaysOfTheWeek = function () {
        return ", 주 중 %s일마다";
    };
    ko.prototype.commaX0ThroughX1 = function () {
        return ", %s에서 %s까지";
    };
    ko.prototype.commaAndX0ThroughX1 = function () {
        return ", 및 %s에서 %s까지";
    };
    ko.prototype.first = function () {
        return "첫 번째";
    };
    ko.prototype.second = function () {
        return "두 번째";
    };
    ko.prototype.third = function () {
        return "세 번째";
    };
    ko.prototype.fourth = function () {
        return "네 번째";
    };
    ko.prototype.fifth = function () {
        return "다섯 번째";
    };
    ko.prototype.commaOnThe = function () {
        return ", 해당 ";
    };
    ko.prototype.spaceX0OfTheMonth = function () {
        return " 해당 월의 %s";
    };
    ko.prototype.lastDay = function () {
        return "마지막 날";
    };
    ko.prototype.commaOnTheLastX0OfTheMonth = function () {
        return ", 해당 월의 마지막 %s";
    };
    ko.prototype.commaOnlyOnX0 = function () {
        return ", %s에만";
    };
    ko.prototype.commaAndOnX0 = function () {
        return ", 및 %s에";
    };
    ko.prototype.commaEveryX0Months = function () {
        return ", %s개월마다";
    };
    ko.prototype.commaOnlyInX0 = function () {
        return ", %s에만";
    };
    ko.prototype.commaOnTheLastDayOfTheMonth = function () {
        return ", 해당 월의 마지막 날에";
    };
    ko.prototype.commaOnTheLastWeekdayOfTheMonth = function () {
        return ", 해당 월의 마지막 평일에";
    };
    ko.prototype.commaDaysBeforeTheLastDayOfTheMonth = function () {
        return ", 해당 월의 마지막 날 %s일 전";
    };
    ko.prototype.firstWeekday = function () {
        return "첫 번째 평일";
    };
    ko.prototype.weekdayNearestDayX0 = function () {
        return "%s일과 가장 가까운 평일";
    };
    ko.prototype.commaOnTheX0OfTheMonth = function () {
        return ", 해당 월의 %s에";
    };
    ko.prototype.commaEveryX0Days = function () {
        return ", %s일마다";
    };
    ko.prototype.commaBetweenDayX0AndX1OfTheMonth = function () {
        return ", 해당 월의 %s일에서 %s일까지";
    };
    ko.prototype.commaOnDayX0OfTheMonth = function () {
        return ", 해당 월의 %s일에";
    };
    ko.prototype.commaEveryMinute = function () {
        return ", 1분마다";
    };
    ko.prototype.commaEveryHour = function () {
        return ", 1시간마다";
    };
    ko.prototype.commaEveryX0Years = function () {
        return ", %s년마다";
    };
    ko.prototype.commaStartingX0 = function () {
        return ", %s부터";
    };
    ko.prototype.daysOfTheWeek = function () {
        return ["일요일", "월요일", "화요일", "수요일", "목요일", "금요일", "토요일"];
    };
    ko.prototype.monthsOfTheYear = function () {
        return ["1월", "2월", "3월", "4월", "5월", "6월", "7월", "8월", "9월", "10월", "11월", "12월"];
    };
    return ko;
}());
exports.ko = ko;


(cronstrue__WEBPACK_IMPORTED_MODULE_0___default().locales)["ko"] = new ko();

/******/ 	return __webpack_exports__;
/******/ })()
;
});