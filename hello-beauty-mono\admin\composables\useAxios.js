// import axios from 'axios';
import { useAuth } from '~/store/auth';
import { useRuntimeConfig } from '#imports';

const createFetchInstance = () => {
  const auth = useAuth();
  const config = useRuntimeConfig();
  const defaultUrl = config.public.API_URL;

  const apiCall = async (url, options = {}) => {
    const { method = 'GET', body, headers = {} } = options;

    const defaultHeaders = {
      'Authorization': `Bearer ${auth.tokenV0y}`,
      ...headers
    };

    // Don't set Content-Type for FormData, let the browser set it
    if (body instanceof FormData) {
      delete defaultHeaders['Content-Type'];
    } else if (body && typeof body === 'object') {
      defaultHeaders['Content-Type'] = 'application/json';
    }

    try {
      const response = await $fetch(url, {
        method,
        baseURL: defaultUrl,
        headers: defaultHeaders,
        body: body instanceof FormData ? body : (body ? JSON.stringify(body) : undefined)
      });

      // Wrap response to match axios format
      return {
        data: response
      };
    } catch (error) {
      console.error('API Error:', error);
      throw error;
    }
  };

  return {
    get: (url) => apiCall(url, { method: 'GET' }),
    post: (url, payload) => apiCall(url, { method: 'POST', body: payload }),
    put: (url, payload) => apiCall(url, { method: 'PUT', body: payload }),
    delete: (url) => apiCall(url, { method: 'DELETE' })
  };
};

const adminGet = (url) => {
  const instance = createFetchInstance();
  return instance.get(url);
};

const adminPost = (url, payload) => {
  const instance = createFetchInstance();
  return instance.post(url, payload);
};

const adminPut = (url, payload) => {
  const instance = createFetchInstance();
  return instance.put(url, payload);
};

const adminDelete = (url) => {
  const instance = createFetchInstance();
  return instance.delete(url);
};

const adminFile = (url, form) => {
  const instance = createFetchInstance();
  return instance.post(url, form);
};

export {
  adminGet, adminPost, adminDelete, adminPut, adminFile
};
