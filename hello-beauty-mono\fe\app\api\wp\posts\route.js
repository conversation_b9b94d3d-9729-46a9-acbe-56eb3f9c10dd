import axios from 'axios';
import { NextResponse } from 'next/server';

const API_BASE_URL = process.env.NEXT_PUBLIC_ARTICLE_API_URL || 'http://localhost:8900';

export async function GET(req) {
  try {
    const { searchParams } = new URL(req.url);
    const limit = searchParams.get('limit') || '10';
    const page = searchParams.get('page') || '1';

    const response = await axios.get(`${API_BASE_URL}/api/articles?limit=${limit}&page=${page}`);

    return NextResponse.json(response.data);
  } catch (error) {
    console.error('Error fetching articles:', error);
    return NextResponse.json({ error: 'Failed to fetch articles' }, { status: 500 });
  }
}
