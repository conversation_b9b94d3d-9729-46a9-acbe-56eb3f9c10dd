import * as unhead_types from 'unhead/types';
import { CreateClientHeadOptions, ActiveHeadEntry } from 'unhead/types';
import { createUnhead } from 'unhead';
import { V as VueHeadClient, U as UseHeadInput, a as UseHeadOptions, b as UseSeoMetaInput } from './shared/vue.DoxLTFJk.mjs';
import { U as UseHeadSafeInput } from './shared/vue.DMlT7xkj.mjs';
import 'vue';

declare const createHeadCore: typeof createUnhead;
declare function resolveUnrefHeadInput(input: any): any;
declare function CapoPlugin(): unhead_types.HeadPluginInput;
declare function createHead(options?: CreateClientHeadOptions): VueHeadClient;
declare function createServerHead(options?: CreateClientHeadOptions): VueHeadClient;
/**
 * @deprecated Please switch to non-legacy version
 */
declare function setHeadInjectionHandler(handler: () => VueHeadClient<any> | undefined): void;
declare function injectHead(): VueHeadClient<any> | undefined;
declare function useHead(input: UseHeadInput, options?: UseHeadOptions): ActiveHeadEntry<UseHeadInput> | void;
declare function useHeadSafe(input: UseHeadSafeInput, options?: UseHeadOptions): ActiveHeadEntry<any> | void;
declare function useSeoMeta(input: UseSeoMetaInput, options?: UseHeadOptions): ActiveHeadEntry<any> | void;
/**
 * @deprecated use `useHead` instead. Advanced use cases should tree shake using import.meta.* if statements.
 */
declare function useServerHead(input: UseHeadInput, options?: UseHeadOptions): ActiveHeadEntry<any> | void;
/**
 * @deprecated use `useHeadSafe` instead. Advanced use cases should tree shake using import.meta.* if statements.
 */
declare function useServerHeadSafe(input: UseHeadSafeInput, options?: UseHeadOptions): ActiveHeadEntry<any> | void;
/**
 * @deprecated use `useSeoMeta` instead. Advanced use cases should tree shake using import.meta.* if statements.
 */
declare function useServerSeoMeta(input: UseSeoMetaInput, options?: UseHeadOptions): ActiveHeadEntry<any> | void;

export { CapoPlugin, createHead, createHeadCore, createServerHead, injectHead, resolveUnrefHeadInput, setHeadInjectionHandler, useHead, useHeadSafe, useSeoMeta, useServerHead, useServerHeadSafe, useServerSeoMeta };
