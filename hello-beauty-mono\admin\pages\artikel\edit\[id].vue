<template>
  <div class="p-3">
    <div class="bg-white rounded-lg border">
      <div class="px-4 pt-4 flex justify-between items-center">
        <h1 class="font-bold text-xl">Edit Artikel</h1>
        <button
          class="btn-secondary"
          @click.prevent="$router.push('/artikel')"
        >
          <icon name="tabler:arrow-left" class="text-lg" />
          Kembali
        </button>
      </div>

      <div v-if="loadingData" class="p-8 text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
        <p class="mt-2 text-gray-500">Memuat data artikel...</p>
      </div>

      <form v-else @submit.prevent="submitForm" class="p-4">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <!-- Main Content -->
          <div class="lg:col-span-2 space-y-6">
            <!-- Title -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Judul Artikel *
              </label>
              <input
                v-model="form.title"
                type="text"
                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder="Masukkan judul artikel"
                required
              />
            </div>

            <!-- Excerpt -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Ringkasan *
              </label>
              <textarea
                v-model="form.excerpt"
                rows="3"
                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder="Ringkasan artikel (maksimal 500 karakter)"
                maxlength="500"
                required
              ></textarea>
              <div class="text-sm text-gray-500 mt-1">
                {{ form.excerpt?.length || 0 }}/500 karakter
              </div>
            </div>

            <!-- Content -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Konten Artikel *
              </label>
              <div class="border border-gray-300 rounded-lg">
                <client-only>
                  <QuillEditor
                    v-model:content="form.content"
                    content-type="html"
                    :options="editorOptions"
                    style="min-height: 400px;"
                  />
                </client-only>
              </div>
            </div>
          </div>

          <!-- Sidebar -->
          <div class="space-y-6">
            <!-- Featured Image -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Gambar Utama
              </label>
              <div class="border-2 border-dashed border-gray-300 rounded-lg p-4">
                <div v-if="imagePreview || article.featuredImageUrl" class="relative">
                  <img
                    :src="imagePreview || article.featuredImageUrl"
                    alt="Preview"
                    class="w-full h-48 object-cover rounded-lg"
                  />
                  <button
                    type="button"
                    @click="removeImage"
                    class="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                  >
                    <icon name="tabler:x" class="w-4 h-4" />
                  </button>
                </div>
                <div v-else class="text-center">
                  <icon name="tabler:photo" class="mx-auto h-12 w-12 text-gray-400" />
                  <div class="mt-2">
                    <label class="cursor-pointer">
                      <span class="text-sm text-primary hover:text-primary-dark">
                        Pilih gambar
                      </span>
                      <input
                        ref="fileInput"
                        type="file"
                        class="hidden"
                        accept="image/*"
                        @change="handleImageUpload"
                      />
                    </label>
                  </div>
                  <p class="text-xs text-gray-500 mt-1">
                    PNG, JPG, WebP hingga 5MB
                  </p>
                </div>
              </div>
            </div>

            <!-- Category -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Kategori *
              </label>
              <input
                v-model="form.category"
                type="text"
                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder="Masukkan kategori"
                required
              />
            </div>

            <!-- Tags -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Tags
              </label>
              <input
                v-model="form.tags"
                type="text"
                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder="Tag1, Tag2, Tag3"
              />
              <p class="text-xs text-gray-500 mt-1">
                Pisahkan dengan koma
              </p>
            </div>

            <!-- Status -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Status *
              </label>
              <select
                v-model="form.status"
                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                required
              >
                <option value="draft">Draft</option>
                <option value="published">Published</option>
                <option value="archived">Archived</option>
              </select>
            </div>

            <!-- SEO Meta -->
            <div class="border-t pt-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">SEO Meta</h3>
              
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    Meta Title
                  </label>
                  <input
                    v-model="form.metaTitle"
                    type="text"
                    class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                    placeholder="Judul untuk SEO"
                  />
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    Meta Description
                  </label>
                  <textarea
                    v-model="form.metaDescription"
                    rows="3"
                    class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                    placeholder="Deskripsi untuk SEO (maksimal 160 karakter)"
                    maxlength="160"
                  ></textarea>
                  <div class="text-sm text-gray-500 mt-1">
                    {{ form.metaDescription?.length || 0 }}/160 karakter
                  </div>
                </div>
              </div>
            </div>

            <!-- Submit Button -->
            <div class="border-t pt-6">
              <button
                type="submit"
                :disabled="loading"
                class="w-full btn-primary flex items-center justify-center"
              >
                <div v-if="loading" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                <icon v-else name="tabler:check" class="mr-2" />
                {{ loading ? 'Menyimpan...' : 'Update Artikel' }}
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { QuillEditor } from '@vueup/vue-quill'
import '@vueup/vue-quill/dist/vue-quill.snow.css'

definePageMeta({
  middleware: "auth-admin",
});

useHead({
  title: "Edit Artikel",
  meta: [
    {
      name: "description",
      content: "Edit Artikel",
    },
  ],
});

const route = useRoute();
const articleId = route.params.id;

const loading = ref(false);
const loadingData = ref(true);
const imagePreview = ref(null);
const selectedFile = ref(null);
const article = ref({});

const form = ref({
  title: '',
  excerpt: '',
  content: '',
  category: '',
  tags: '',
  status: 'draft',
  metaTitle: '',
  metaDescription: ''
});

const editorOptions = {
  theme: 'snow',
  modules: {
    toolbar: [
      ['bold', 'italic', 'underline', 'strike'],
      ['blockquote', 'code-block'],
      [{ 'header': 1 }, { 'header': 2 }],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'script': 'sub'}, { 'script': 'super' }],
      [{ 'indent': '-1'}, { 'indent': '+1' }],
      [{ 'direction': 'rtl' }],
      [{ 'size': ['small', false, 'large', 'huge'] }],
      [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
      [{ 'color': [] }, { 'background': [] }],
      [{ 'font': [] }],
      [{ 'align': [] }],
      ['clean'],
      ['link', 'image']
    ]
  }
};

const getArticle = async () => {
  try {
    loadingData.value = true;
    const { data } = await adminGet(`/article/${articleId}`);
    article.value = data.data;

    // Populate form
    form.value = {
      title: article.value.title,
      excerpt: article.value.excerpt,
      content: article.value.content,
      category: article.value.category,
      tags: article.value.tags ? article.value.tags.join(', ') : '',
      status: article.value.status,
      metaTitle: article.value.metaTitle || '',
      metaDescription: article.value.metaDescription || ''
    };
  } catch (error) {
    console.error('Error fetching article:', error);
    useNuxtApp().$toast.error('Gagal memuat artikel');
    await navigateTo('/artikel');
  } finally {
    loadingData.value = false;
  }
};

const handleImageUpload = (event) => {
  const file = event.target.files[0];
  if (!file) return;

  // Validate file type
  if (!file.type.startsWith('image/')) {
    useNuxtApp().$toast.error('File harus berupa gambar');
    return;
  }

  // Validate file size (5MB)
  if (file.size > 5 * 1024 * 1024) {
    useNuxtApp().$toast.error('Ukuran file maksimal 5MB');
    return;
  }

  selectedFile.value = file;

  // Create preview
  const reader = new FileReader();
  reader.onload = (e) => {
    imagePreview.value = e.target.result;
  };
  reader.readAsDataURL(file);
};

const removeImage = () => {
  imagePreview.value = null;
  selectedFile.value = null;
  if (process.client) {
    const fileInput = document.querySelector('input[type="file"]');
    if (fileInput) fileInput.value = '';
  }
};

const submitForm = async () => {
  try {
    loading.value = true;

    const formData = new FormData();
    formData.append('title', form.value.title);
    formData.append('excerpt', form.value.excerpt);
    formData.append('content', form.value.content);
    formData.append('category', form.value.category);
    formData.append('tags', form.value.tags);
    formData.append('status', form.value.status);
    formData.append('metaTitle', form.value.metaTitle || form.value.title);
    formData.append('metaDescription', form.value.metaDescription || form.value.excerpt);

    if (selectedFile.value) {
      formData.append('featuredImage', selectedFile.value);
    }

    await adminPut(`/article/${articleId}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });

    useNuxtApp().$toast.success('Artikel berhasil diupdate');
    await navigateTo('/artikel');
  } catch (error) {
    console.error('Error updating article:', error);
    useNuxtApp().$toast.error('Gagal mengupdate artikel');
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  getArticle();
});
</script>
