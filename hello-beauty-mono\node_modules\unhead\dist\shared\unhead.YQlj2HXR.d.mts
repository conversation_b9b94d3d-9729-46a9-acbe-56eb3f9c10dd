import { U as Unhead, a as UseScriptInput, b as UseScriptOptions, c as UseScriptReturn, d as UseScriptResolvedInput } from './unhead.BxIzrSMV.mjs';

/**
 * @deprecated compute key manually
 */
declare function resolveScriptKey(input: UseScriptResolvedInput): string;
/**
 * Load third-party scripts with SSR support and a proxied API.
 *
 * @see https://unhead.unjs.io/usage/composables/use-script
 */
declare function useScript<T extends Record<symbol | string, any> = Record<symbol | string, any>>(head: Unhead<any>, _input: UseScriptInput, _options?: UseScriptOptions<T>): UseScriptReturn<T>;

export { resolveScriptKey as r, useScript as u };
