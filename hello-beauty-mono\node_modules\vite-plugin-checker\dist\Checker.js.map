{"version": 3, "sources": ["../src/Checker.ts"], "sourcesContent": ["import invariant from 'tiny-invariant'\nimport type {\n  BuildIn<PERSON><PERSON><PERSON><PERSON><PERSON>s,\n  BuildInCheckers,\n  CreateDiagnostic,\n  ServeAndBuildChecker,\n} from './types.js'\nimport { isInVitestEntryThread, isMainThread } from './utils.js'\nimport { createScript, type Script } from './worker.js'\n\nif (!(isMainThread || isInVitestEntryThread)) {\n  process.stdout.isTTY = true\n}\n\ninterface CheckerMeta<T extends BuildInCheckerNames> {\n  name: T\n  absFilePath: string\n  createDiagnostic: CreateDiagnostic<T>\n  build: ServeAndBuildChecker['build']\n  script?: Script<any>\n}\n\nexport abstract class Checker<T extends BuildInCheckerNames>\n  implements CheckerMeta<T>\n{\n  public static logger: ((...v: string[]) => unknown)[] = []\n\n  public static log(...args: any[]) {\n    for (const fn of Checker.logger) {\n      fn(...args)\n    }\n  }\n\n  public name: T\n  public absFilePath: string\n  public createDiagnostic: CreateDiagnostic<T>\n  public build: ServeAndBuildChecker['build']\n  public script?: Script<any>\n\n  public constructor({\n    name,\n    absFilePath,\n    createDiagnostic,\n    build,\n  }: CheckerMeta<T>) {\n    this.name = name\n    this.absFilePath = absFilePath\n    this.build = build\n    this.createDiagnostic = createDiagnostic\n    this.build = build\n  }\n\n  public prepare() {\n    const script = createScript<Pick<BuildInCheckers, T>>({\n      absFilename: this.absFilePath,\n      buildBin: this.build.buildBin,\n      serverChecker: { createDiagnostic: this.createDiagnostic },\n    })!\n\n    this.script = script\n    return script\n  }\n\n  public initMainThread() {\n    invariant(\n      this.script,\n      `script should be created in 'prepare', but got ${this.script}`,\n    )\n\n    if (isMainThread || isInVitestEntryThread) {\n      const createServeAndBuild = this.script.mainScript()\n      return createServeAndBuild\n    }\n\n    return\n  }\n\n  public initWorkerThread() {\n    invariant(\n      this.script,\n      `script should be created in 'prepare', but got ${this.script}`,\n    )\n\n    if (!(isMainThread || isInVitestEntryThread)) {\n      this.script.workerScript()\n    }\n  }\n}\n"], "mappings": "AAAA,OAAO,eAAe;AAOtB,SAAS,uBAAuB,oBAAoB;AACpD,SAAS,oBAAiC;AAE1C,IAAI,EAAE,gBAAgB,wBAAwB;AAC5C,UAAQ,OAAO,QAAQ;AACzB;AAUO,MAAe,QAEtB;AAAA,EACE,OAAc,SAA0C,CAAC;AAAA,EAEzD,OAAc,OAAO,MAAa;AAChC,eAAW,MAAM,QAAQ,QAAQ;AAC/B,SAAG,GAAG,IAAI;AAAA,IACZ;AAAA,EACF;AAAA,EAEO;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAEA,YAAY;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAmB;AACjB,SAAK,OAAO;AACZ,SAAK,cAAc;AACnB,SAAK,QAAQ;AACb,SAAK,mBAAmB;AACxB,SAAK,QAAQ;AAAA,EACf;AAAA,EAEO,UAAU;AACf,UAAM,SAAS,aAAuC;AAAA,MACpD,aAAa,KAAK;AAAA,MAClB,UAAU,KAAK,MAAM;AAAA,MACrB,eAAe,EAAE,kBAAkB,KAAK,iBAAiB;AAAA,IAC3D,CAAC;AAED,SAAK,SAAS;AACd,WAAO;AAAA,EACT;AAAA,EAEO,iBAAiB;AACtB;AAAA,MACE,KAAK;AAAA,MACL,kDAAkD,KAAK,MAAM;AAAA,IAC/D;AAEA,QAAI,gBAAgB,uBAAuB;AACzC,YAAM,sBAAsB,KAAK,OAAO,WAAW;AACnD,aAAO;AAAA,IACT;AAEA;AAAA,EACF;AAAA,EAEO,mBAAmB;AACxB;AAAA,MACE,KAAK;AAAA,MACL,kDAAkD,KAAK,MAAM;AAAA,IAC/D;AAEA,QAAI,EAAE,gBAAgB,wBAAwB;AAC5C,WAAK,OAAO,aAAa;AAAA,IAC3B;AAAA,EACF;AACF;", "names": []}