{"name": "admin", "version": "0.1.0", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev -p 8901", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint:js": "eslint --ext \".ts,.vue\" --ignore-path .gitignore .", "lint:prettier": "prettier --check .", "lint": "yarn lint:js && yarn lint:prettier", "lintfix": "prettier --write --list-different . && yarn lint:js --fix", "start": "node .output/server/index.mjs"}, "devDependencies": {"@nuxt/devtools": "^1.0.6", "@nuxt/eslint": "^0.3.12", "@nuxt/eslint-config": "^0.3.12", "@nuxtjs/eslint-config-typescript": "^12.1.0", "@nuxtjs/tailwindcss": "^6.12.0", "@typescript-eslint/parser": "^6.19.0", "autoprefixer": "^10.4.14", "axios": "^1.7.2", "eslint": "^8.2.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.25.2", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.26.0", "postcss": "^8.4.21", "prettier": "^3.2.5", "tailwindcss": "^3.2.7", "typescript": "^5.4.5"}, "dependencies": {"@pinia/nuxt": "^0.5.1", "@vueup/vue-quill": "^1.2.0", "apexcharts": "^4.0.0", "dotenv": "^16.4.5", "lodash": "^4.17.21", "md5": "^2.3.0", "moment": "^2.29.4", "nuxt": "^3.8.0", "nuxt-icon": "^0.4.1", "pinia": "^2.1.7", "vue-datepicker-next": "^1.0.3", "vue-final-modal": "^4.2.0", "vue-toastification": "^2.0.0-rc.5", "vue3-apexcharts": "^1.7.0", "vue3-side-panel": "^1.3.0", "vuedraggable": "^4.1.0"}, "engines": {"node": "20.x"}}