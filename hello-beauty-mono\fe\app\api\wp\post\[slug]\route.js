import axios from 'axios';
import { NextResponse } from 'next/server';

const API_BASE_URL = process.env.NEXT_PUBLIC_ARTICLE_API_URL || 'http://localhost:8900';

export async function GET(req, { params }) {
  const { slug } = params; // Extract slug from the parameters

  if (!slug) {
    return NextResponse.json({ error: 'Slug is required' }, { status: 400 });
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/api/articles/${slug}`);

    return NextResponse.json(response.data);
  } catch (error) {
    console.error('Error fetching article:', error);
    return NextResponse.json({ error: 'Failed to fetch article' }, { status: 500 });
  }
}
