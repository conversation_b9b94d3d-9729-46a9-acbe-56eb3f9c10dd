import{Q as E,w as x}from"./er2hcepb.js";import{_ as N}from"./stacktrace-list.vue-bbs3noa6.js";import{k as y,l as m,S as u,a0 as w,$ as C,U as f,a5 as d,W as _,M as S,Q as i,a6 as $}from"./vendor/json-editor-vue-gv8v06ez.js";import"./vendor/unocss-f0kk6o39.js";import"./vendor/shiki-ml6g43ls.js";import"./filepath-item.vue-n2otfocd.js";const O=/(^|@)\S+:\d+/,h=/^\s*at .*(\S+:\d+|\(native\))/m,R=/^(eval@)?(\[native code\])?$/;function F(e,a){if(typeof e.stacktrace<"u"||typeof e["opera#sourceloc"]<"u")return V(e);if(e.stack&&e.stack.match(h))return I(e);if(e.stack)return A(e);throw new Error("Cannot parse given Error object")}function v(e){if(!e.includes(":"))return[e,void 0,void 0];const r=/(.+?)(?::(\d+))?(?::(\d+))?$/.exec(e.replace(/[()]/g,""));return[r[1],r[2]||void 0,r[3]||void 0]}function p(e,a){return e}function I(e,a){return b(e.stack)}function b(e,a){return p(e.split(`
`).filter(t=>!!t.match(h))).map(t=>{t.includes("(eval ")&&(t=t.replace(/eval code/g,"eval").replace(/(\(eval at [^()]*)|(,.*$)/g,""));let o=t.replace(/^\s+/,"").replace(/\(eval code/g,"(").replace(/^.*?\s+/,"");const n=o.match(/ (\(.+\)$)/);o=n?o.replace(n[0],""):o;const c=v(n?n[1]:o),s=n&&o||void 0,l=["eval","<anonymous>"].includes(c[0])?void 0:c[0];return{function:s,file:l,line:c[1]?+c[1]:void 0,col:c[2]?+c[2]:void 0,raw:t}})}function A(e,a){return T(e.stack)}function T(e,a){return p(e.split(`
`).filter(t=>!t.match(R))).map(t=>{if(t.includes(" > eval")&&(t=t.replace(/ line (\d+)(?: > eval line \d+)* > eval:\d+:\d+/g,":$1")),!t.includes("@")&&!t.includes(":"))return{function:t};{const o=/(([^\n\r"\u2028\u2029]*".[^\n\r"\u2028\u2029]*"[^\n\r@\u2028\u2029]*(?:@[^\n\r"\u2028\u2029]*"[^\n\r@\u2028\u2029]*)*(?:[\n\r\u2028\u2029][^@]*)?)?[^@]*)@/,n=t.match(o),c=n&&n[1]?n[1]:void 0,s=v(t.replace(o,""));return{function:c,file:s[0],line:s[1]?+s[1]:void 0,col:s[2]?+s[2]:void 0,raw:t}}})}function V(e,a){return!e.stacktrace||e.message.includes(`
`)&&e.message.split(`
`).length>e.stacktrace.split(`
`).length?P(e):e.stack?L(e):B(e)}function P(e,a){const r=/Line (\d+).*script (?:in )?(\S+)/i,t=e.message.split(`
`),o=[];for(let n=2,c=t.length;n<c;n+=2){const s=r.exec(t[n]);s&&o.push({file:s[2],line:+s[1],raw:t[n]})}return p(o)}function B(e,a){const r=/Line (\d+).*script (?:in )?(\S+)(?:: In function (\S+))?$/i,t=e.stacktrace.split(`
`),o=[];for(let n=0,c=t.length;n<c;n+=2){const s=r.exec(t[n]);s&&o.push({function:s[3]||void 0,file:s[2],line:s[1]?+s[1]:void 0,raw:t[n]})}return p(o)}function L(e,a){return p(e.stack.split(`
`).filter(t=>!!t.match(O)&&!t.match(/^Error created at/))).map(t=>{const o=t.split("@"),n=v(o.pop()),c=o.shift()||"",s=c.replace(/<anonymous function(: (\w+))?>/,"$2").replace(/\([^)]*\)/g,"")||void 0;let l;c.match(/\(([^)]*)\)/)&&(l=c.replace(/^[^(]+\(([^)]*)\)$/,"$1"));const k=l===void 0||l==="[arguments not available]"?void 0:l.split(",");return{function:s,args:k,file:n[0],line:n[1]?+n[1]:void 0,col:n[2]?+n[2]:void 0,raw:t}})}function M(e){return e.map(a=>({functionName:a.function,args:a.args,fileName:a.file,lineNumber:a.line,columnNumber:a.col,source:a.raw}))}function g(e,a){return M(F(e))}const X={p6:""},D={key:0},G={"text-6xl":""},K={key:0,op75:""},Q={my4:"","text-xl":"","text-red":""},U={key:1,"of-auto":"",rounded:"","bg-active":"",p2:""},W=["textContent"],j={key:1,op50:""},ee=y({__name:"error",setup(e){const a=E(),r=m(()=>{const o=a.value?.nuxt?.payload?.error;return o&&(console.error("[Nuxt DevTools] Error in payload:"),console.error(o),console.error({...o})),o}),t=m(()=>{if(!r.value?.stack)return[];try{return r.value.stack.startsWith("<pre>")?g({stack:r.value.stack.replace(/<.*?>/g,"")}):g(r.value)}catch(o){return console.error(o),[]}});return(o,n)=>{const c=x,s=N;return i(),u("div",X,[r.value?(i(),u("div",D,[w(c,{n:"red",icon:"i-carbon-warning-alt-filled",mb5:""},{default:C(()=>n[0]||(n[0]=[$(" Error occurred in this page ")])),_:1}),f("div",G,d(r.value.statusCode||"Client Error"),1),r.value.statusMessage?(i(),u("div",K,d(r.value.statusMessage),1)):_("",!0),f("div",Q,d(r.value.message||r.value.description||"Unknown error"),1),t.value.length||r.value.stack?(i(),u("div",U,[n[1]||(n[1]=f("div",{px1:"",op50:""}," Stacktrace ",-1)),t.value.length?(i(),S(s,{key:0,px2:"",stacktrace:t.value},null,8,["stacktrace"])):(i(),u("pre",{key:1,textContent:d(r.value.stack)},null,8,W))])):_("",!0)])):(i(),u("div",j," No error "))])}}});export{ee as default};
