{"name": "fuse.js", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://kiro.me"}, "main": "./dist/fuse.common.js", "module": "./dist/fuse.esm.js", "unpkg": "./dist/fuse.js", "jsdelivr": "./dist/fuse.js", "typings": "./dist/fuse.d.ts", "sideEffects": false, "files": ["dist"], "version": "6.6.2", "description": "Lightweight fuzzy-search", "license": "Apache-2.0", "repository": {"type": "git", "url": "https://github.com/krisk/Fuse.git"}, "homepage": "http://fusejs.io", "keywords": ["fuzzy", "search", "bitap"], "scripts": {"dev": "rollup -w -c scripts/configs.js --environment TARGET:umd-dev-full", "dev:cjs": "rollup -w -c scripts/configs.js --environment TARGET:commonjs-full", "dev:esm": "rollup -w -c scripts/configs.js --environment TARGET:esm-dev-full", "build": "rm -r dist && mkdir dist && node ./scripts/build.main.js", "test": "jest", "lint": "eslint src scripts test", "release": "./scripts/release.sh", "docs:bump": "node ./scripts/bump-docs.js", "docs:dev": "vuepress dev docs", "docs:build": "vuepress build docs", "docs:release": "./scripts/deploy-docs.sh", "prepare": "husky install"}, "standard-version": {"scripts": {"postbump": "yarn build && yarn lint && yarn test 2>/dev/null", "precommit": "git add dist/*.js dist/*.ts"}}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "devDependencies": {"@babel/cli": "^7.2.3", "@babel/core": "^7.3.4", "@babel/eslint-parser": "^7.16.3", "@babel/plugin-proposal-object-rest-spread": "7.16.0", "@babel/preset-env": "7.16.0", "@babel/preset-typescript": "7.16.0", "@commitlint/cli": "^14.1.0", "@commitlint/config-conventional": "^14.1.0", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-node-resolve": "^13.0.6", "@rollup/plugin-replace": "^3.0.0", "@types/jest": "27.0.2", "@vuepress/plugin-google-analytics": "^1.4.0", "@vuepress/plugin-register-components": "^1.5.2", "babel-loader": "^8.0.5", "codemirror": "5.63.3", "eslint": "8.2.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-vue": "8.0.3", "faker": "5.5.3", "husky": "^7.0.0", "jest": "^27.3.1", "prettier": "2.4.1", "replace-in-file": "^6.1.0", "rimraf": "3.0.2", "rollup": "^2.61.1", "rollup-plugin-copy": "3.4.0", "standard-version": "^9.3.2", "terser-webpack-plugin": "5.2.5", "typescript": "^4.4.4", "vue-codemirror": "^4.0.6", "vue-eslint-parser": "^8.0.1", "vuepress": "^1.4.0", "vuepress-plugin-element-tabs": "^0.2.8", "vuepress-plugin-google-adsense": "^0.2.1", "vuepress-plugin-smooth-scroll": "^0.0.10", "vuepress-plugin-social-share": "^1.1.0", "webpack": "4.46.0", "webpack-cli": "^4.9.1"}, "engines": {"node": ">=10"}}