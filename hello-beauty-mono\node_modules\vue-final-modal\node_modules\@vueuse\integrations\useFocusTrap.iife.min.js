var VueDemi=function(n,e,c){if(n.install)return n;if(!e)return console.error("[vue-demi] no Vue instance found, please be sure to import `vue` before `vue-demi`."),n;if(e.version.slice(0,4)==="2.7."){let r=function(t,s){var o,v={},l={config:e.config,use:e.use.bind(e),mixin:e.mixin.bind(e),component:e.component.bind(e),provide:function(a,f){return v[a]=f,this},directive:function(a,f){return f?(e.directive(a,f),l):e.directive(a)},mount:function(a,f){return o||(o=new e(Object.assign({propsData:s},t,{provide:Object.assign(v,t.provide)})),o.$mount(a,f),o)},unmount:function(){o&&(o.$destroy(),o=void 0)}};return l};var d=r;for(var i in e)n[i]=e[i];n.isVue2=!0,n.isVue3=!1,n.install=function(){},n.Vue=e,n.Vue2=e,n.version=e.version,n.warn=e.util.warn,n.hasInjectionContext=function(){return!!n.getCurrentInstance()},n.createApp=r}else if(e.version.slice(0,2)==="2.")if(c){for(var i in c)n[i]=c[i];n.isVue2=!0,n.isVue3=!1,n.install=function(){},n.Vue=e,n.Vue2=e,n.version=e.version,n.hasInjectionContext=function(){return!!n.getCurrentInstance()}}else console.error("[vue-demi] no VueCompositionAPI instance found, please be sure to import `@vue/composition-api` before `vue-demi`.");else if(e.version.slice(0,2)==="3."){for(var i in e)n[i]=e[i];n.isVue2=!1,n.isVue3=!0,n.install=function(){},n.Vue=e,n.Vue2=void 0,n.version=e.version,n.set=function(r,t,s){return Array.isArray(r)?(r.length=Math.max(r.length,t),r.splice(t,1,s),s):(r[t]=s,s)},n.del=function(r,t){if(Array.isArray(r)){r.splice(t,1);return}delete r[t]}}else console.error("[vue-demi] Vue version "+e.version+" is unsupported.");return n}((globalThis||self).VueDemi=(globalThis||self).VueDemi||(typeof VueDemi<"u"?VueDemi:{}),(globalThis||self).Vue||(typeof Vue<"u"?Vue:void 0),(globalThis||self).VueCompositionAPI||(typeof VueCompositionAPI<"u"?VueCompositionAPI:void 0));(function(n,e,c,i){"use strict";function d(r,t={}){let s;const{immediate:o,...v}=t,l=c.ref(!1),a=c.ref(!1),f=u=>s&&s.activate(u),p=u=>s&&s.deactivate(u),h=()=>{s&&(s.pause(),a.value=!0)},b=()=>{s&&(s.unpause(),a.value=!1)};return c.watch(()=>e.unrefElement(r),u=>{u&&(s=i.createFocusTrap(u,{...v,onActivate(){l.value=!0,t.onActivate&&t.onActivate()},onDeactivate(){l.value=!1,t.onDeactivate&&t.onDeactivate()}}),o&&f())},{flush:"post"}),e.tryOnScopeDispose(()=>p()),{hasFocus:l,isPaused:a,activate:f,deactivate:p,pause:h,unpause:b}}n.useFocusTrap=d})(this.VueUse=this.VueUse||{},VueUse,VueDemi,focusTrap);
