{"version": 3, "sources": ["../../../src/checkers/typescript/main.ts"], "sourcesContent": ["import os from 'node:os'\nimport path from 'node:path'\nimport { fileURLToPath } from 'node:url'\nimport { parentPort } from 'node:worker_threads'\nimport colors from 'picocolors'\nimport invariant from 'tiny-invariant'\nimport type * as typescript from 'typescript'\n\nimport { Checker } from '../../Checker.js'\nimport {\n  consoleLog,\n  diagnosticToRuntimeError,\n  diagnosticToTerminalLog,\n  ensureCall,\n  normalizeTsDiagnostic,\n  toClientPayload,\n  wrapCheckerSummary,\n} from '../../logger.js'\nimport {\n  ACTION_TYPES,\n  type CreateDiagnostic,\n  type DiagnosticToRuntime,\n} from '../../types.js'\n\nconst __filename = fileURLToPath(import.meta.url)\nlet createServeAndBuild: any\n\nconst createDiagnostic: CreateDiagnostic<'typescript'> = (pluginConfig) => {\n  let overlay = true\n  let terminal = true\n  let currDiagnostics: DiagnosticToRuntime[] = []\n\n  return {\n    config: async ({ enableOverlay, enableTerminal }) => {\n      overlay = enableOverlay\n      terminal = enableTerminal\n    },\n    async configureServer({ root }) {\n      invariant(pluginConfig.typescript, 'config.typescript should be `false`')\n      const finalConfig =\n        pluginConfig.typescript === true\n          ? {\n              root,\n              tsconfigPath: 'tsconfig.json',\n              typescriptPath: 'typescript',\n            }\n          : {\n              root: pluginConfig.typescript.root ?? root,\n              tsconfigPath:\n                pluginConfig.typescript.tsconfigPath ?? 'tsconfig.json',\n              typescriptPath:\n                pluginConfig.typescript.typescriptPath ?? 'typescript',\n            }\n\n      let configFile: string | undefined\n      const ts: typeof typescript = await import(\n        finalConfig.typescriptPath\n      ).then((r) => r.default || r)\n      configFile = ts.findConfigFile(\n        finalConfig.root,\n        ts.sys.fileExists,\n        finalConfig.tsconfigPath,\n      )\n\n      if (configFile === undefined) {\n        throw Error(\n          `Failed to find a valid tsconfig.json: ${finalConfig.tsconfigPath} at ${finalConfig.root} is not a valid tsconfig`,\n        )\n      }\n\n      let logChunk = ''\n\n      // https://github.com/microsoft/TypeScript/blob/a545ab1ac2cb24ff3b1aaf0bfbfb62c499742ac2/src/compiler/watch.ts#L12-L28\n      const reportDiagnostic = (diagnostic: typescript.Diagnostic) => {\n        const normalizedDiagnostic = normalizeTsDiagnostic(diagnostic)\n        if (normalizedDiagnostic === null) {\n          return\n        }\n\n        currDiagnostics.push(diagnosticToRuntimeError(normalizedDiagnostic))\n        logChunk +=\n          os.EOL + diagnosticToTerminalLog(normalizedDiagnostic, 'TypeScript')\n      }\n\n      const reportWatchStatusChanged: typescript.WatchStatusReporter = (\n        diagnostic,\n        _newLine,\n        _options,\n        errorCount,\n        // eslint-disable-next-line max-params\n      ) => {\n        if (diagnostic.code === 6031) return\n        // https://github.com/microsoft/TypeScript/issues/32542\n        // https://github.com/microsoft/TypeScript/blob/dc237b317ed4bbccd043ddda802ffde00362a387/src/compiler/diagnosticMessages.json#L4086-L4088\n        switch (diagnostic.code) {\n          case 6031:\n          case 6032:\n            // clear current error and use the newer errors\n            logChunk = ''\n            // currErr = null\n            currDiagnostics = []\n            return\n          case 6193: // 1 Error\n          case 6194: // 0 errors or 2+ errors\n            if (overlay) {\n              parentPort?.postMessage({\n                type: ACTION_TYPES.overlayError,\n                payload: toClientPayload('typescript', currDiagnostics),\n              })\n            }\n        }\n\n        ensureCall(() => {\n          if (errorCount === 0) {\n            logChunk = ''\n          }\n\n          if (terminal) {\n            const color = errorCount && errorCount > 0 ? 'red' : 'green'\n            consoleLog(\n              colors[color](\n                logChunk +\n                  os.EOL +\n                  wrapCheckerSummary(\n                    'TypeScript',\n                    diagnostic.messageText.toString(),\n                  ),\n              ),\n              errorCount ? 'error' : 'info',\n            )\n          }\n        })\n      }\n\n      // https://github.com/microsoft/TypeScript/issues/32385\n      // https://github.com/microsoft/TypeScript/pull/33082/files\n      const createProgram = ts.createEmitAndSemanticDiagnosticsBuilderProgram\n\n      if (\n        typeof pluginConfig.typescript === 'object' &&\n        pluginConfig.typescript.buildMode\n      ) {\n        const host = ts.createSolutionBuilderWithWatchHost(\n          ts.sys,\n          createProgram,\n          reportDiagnostic,\n          undefined,\n          reportWatchStatusChanged,\n        )\n\n        ts.createSolutionBuilderWithWatch(host, [configFile], {}).build()\n      } else {\n        const host = ts.createWatchCompilerHost(\n          configFile,\n          { noEmit: true },\n          ts.sys,\n          createProgram,\n          reportDiagnostic,\n          reportWatchStatusChanged,\n        )\n\n        ts.createWatchProgram(host)\n      }\n    },\n  }\n}\n\nexport class TscChecker extends Checker<'typescript'> {\n  public constructor() {\n    super({\n      name: 'typescript',\n      absFilePath: __filename,\n      build: {\n        buildBin: (config) => {\n          if (typeof config.typescript === 'object') {\n            const {\n              root = '',\n              tsconfigPath = '',\n              buildMode,\n            } = config.typescript\n\n            // Compiler option '--noEmit' may not be used with '--build'\n            const args = [buildMode ? '-b' : '--noEmit']\n\n            // Custom config path\n            let projectPath = ''\n            if (root || tsconfigPath) {\n              projectPath = root ? path.join(root, tsconfigPath) : tsconfigPath\n            }\n\n            if (projectPath) {\n              // In build mode, the tsconfig path is an argument to -b, e.g. \"tsc -b [path]\"\n              if (buildMode) {\n                args.push(projectPath)\n              } else {\n                args.push('-p', projectPath)\n              }\n            }\n\n            return ['tsc', args]\n          }\n\n          return ['tsc', ['--noEmit']]\n        },\n      },\n      createDiagnostic,\n    })\n  }\n\n  public init() {\n    const _createServeAndBuild = super.initMainThread()\n    createServeAndBuild = _createServeAndBuild\n    super.initWorkerThread()\n  }\n}\n\nexport { createServeAndBuild }\nconst tscChecker = new TscChecker()\ntscChecker.prepare()\ntscChecker.init()\n"], "mappings": "AAAA,OAAO,QAAQ;AACf,OAAO,UAAU;AACjB,SAAS,qBAAqB;AAC9B,SAAS,kBAAkB;AAC3B,OAAO,YAAY;AACnB,OAAO,eAAe;AAGtB,SAAS,eAAe;AACxB;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP;AAAA,EACE;AAAA,OAGK;AAEP,MAAM,aAAa,cAAc,YAAY,GAAG;AAChD,IAAI;AAEJ,MAAM,mBAAmD,CAAC,iBAAiB;AACzE,MAAI,UAAU;AACd,MAAI,WAAW;AACf,MAAI,kBAAyC,CAAC;AAE9C,SAAO;AAAA,IACL,QAAQ,OAAO,EAAE,eAAe,eAAe,MAAM;AACnD,gBAAU;AACV,iBAAW;AAAA,IACb;AAAA,IACA,MAAM,gBAAgB,EAAE,KAAK,GAAG;AAC9B,gBAAU,aAAa,YAAY,qCAAqC;AACxE,YAAM,cACJ,aAAa,eAAe,OACxB;AAAA,QACE;AAAA,QACA,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB,IACA;AAAA,QACE,MAAM,aAAa,WAAW,QAAQ;AAAA,QACtC,cACE,aAAa,WAAW,gBAAgB;AAAA,QAC1C,gBACE,aAAa,WAAW,kBAAkB;AAAA,MAC9C;AAEN,UAAI;AACJ,YAAM,KAAwB,MAAM,OAClC,YAAY,gBACZ,KAAK,CAAC,MAAM,EAAE,WAAW,CAAC;AAC5B,mBAAa,GAAG;AAAA,QACd,YAAY;AAAA,QACZ,GAAG,IAAI;AAAA,QACP,YAAY;AAAA,MACd;AAEA,UAAI,eAAe,QAAW;AAC5B,cAAM;AAAA,UACJ,yCAAyC,YAAY,YAAY,OAAO,YAAY,IAAI;AAAA,QAC1F;AAAA,MACF;AAEA,UAAI,WAAW;AAGf,YAAM,mBAAmB,CAAC,eAAsC;AAC9D,cAAM,uBAAuB,sBAAsB,UAAU;AAC7D,YAAI,yBAAyB,MAAM;AACjC;AAAA,QACF;AAEA,wBAAgB,KAAK,yBAAyB,oBAAoB,CAAC;AACnE,oBACE,GAAG,MAAM,wBAAwB,sBAAsB,YAAY;AAAA,MACvE;AAEA,YAAM,2BAA2D,CAC/D,YACA,UACA,UACA,eAEG;AA1FX;AA2FQ,YAAI,WAAW,SAAS,KAAM;AAG9B,gBAAQ,WAAW,MAAM;AAAA,UACvB,KAAK;AAAA,UACL,KAAK;AAEH,uBAAW;AAEX,8BAAkB,CAAC;AACnB;AAAA,UACF,KAAK;AAAA;AAAA,UACL,KAAK;AACH,gBAAI,SAAS;AACX,sDAAY,YAAY;AAAA,gBACtB,MAAM,aAAa;AAAA,gBACnB,SAAS,gBAAgB,cAAc,eAAe;AAAA,cACxD;AAAA,YACF;AAAA,QACJ;AAEA,mBAAW,MAAM;AACf,cAAI,eAAe,GAAG;AACpB,uBAAW;AAAA,UACb;AAEA,cAAI,UAAU;AACZ,kBAAM,QAAQ,cAAc,aAAa,IAAI,QAAQ;AACrD;AAAA,cACE,OAAO,KAAK;AAAA,gBACV,WACE,GAAG,MACH;AAAA,kBACE;AAAA,kBACA,WAAW,YAAY,SAAS;AAAA,gBAClC;AAAA,cACJ;AAAA,cACA,aAAa,UAAU;AAAA,YACzB;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAIA,YAAM,gBAAgB,GAAG;AAEzB,UACE,OAAO,aAAa,eAAe,YACnC,aAAa,WAAW,WACxB;AACA,cAAM,OAAO,GAAG;AAAA,UACd,GAAG;AAAA,UACH;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAEA,WAAG,+BAA+B,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC,EAAE,MAAM;AAAA,MAClE,OAAO;AACL,cAAM,OAAO,GAAG;AAAA,UACd;AAAA,UACA,EAAE,QAAQ,KAAK;AAAA,UACf,GAAG;AAAA,UACH;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAEA,WAAG,mBAAmB,IAAI;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AACF;AAEO,MAAM,mBAAmB,QAAsB;AAAA,EAC7C,cAAc;AACnB,UAAM;AAAA,MACJ,MAAM;AAAA,MACN,aAAa;AAAA,MACb,OAAO;AAAA,QACL,UAAU,CAAC,WAAW;AACpB,cAAI,OAAO,OAAO,eAAe,UAAU;AACzC,kBAAM;AAAA,cACJ,OAAO;AAAA,cACP,eAAe;AAAA,cACf;AAAA,YACF,IAAI,OAAO;AAGX,kBAAM,OAAO,CAAC,YAAY,OAAO,UAAU;AAG3C,gBAAI,cAAc;AAClB,gBAAI,QAAQ,cAAc;AACxB,4BAAc,OAAO,KAAK,KAAK,MAAM,YAAY,IAAI;AAAA,YACvD;AAEA,gBAAI,aAAa;AAEf,kBAAI,WAAW;AACb,qBAAK,KAAK,WAAW;AAAA,cACvB,OAAO;AACL,qBAAK,KAAK,MAAM,WAAW;AAAA,cAC7B;AAAA,YACF;AAEA,mBAAO,CAAC,OAAO,IAAI;AAAA,UACrB;AAEA,iBAAO,CAAC,OAAO,CAAC,UAAU,CAAC;AAAA,QAC7B;AAAA,MACF;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEO,OAAO;AACZ,UAAM,uBAAuB,MAAM,eAAe;AAClD,0BAAsB;AACtB,UAAM,iBAAiB;AAAA,EACzB;AACF;AAGA,MAAM,aAAa,IAAI,WAAW;AAClC,WAAW,QAAQ;AACnB,WAAW,KAAK;", "names": []}