{"name": "@eslint/config-inspector", "type": "module", "version": "0.4.12", "description": "A visual tool for inspecting and understanding your ESLint flat configs", "license": "Apache-2.0", "funding": "https://opencollective.com/eslint", "homepage": "https://github.com/eslint/config-inspector#readme", "repository": {"type": "git", "url": "git+https://github.com/eslint/config-inspector.git"}, "bugs": "https://github.com/eslint/config-inspector/issues", "bin": {"@eslint/config-inspector": "./bin.mjs", "eslint-config-inspector": "./bin.mjs"}, "files": ["*.mjs", "dist"], "peerDependencies": {"eslint": "^8.50.0 || ^9.0.0"}, "dependencies": {"bundle-require": "^5.0.0", "cac": "^6.7.14", "chokidar": "^3.6.0", "esbuild": "^0.21.5", "fast-glob": "^3.3.2", "find-up": "^7.0.0", "get-port-please": "^3.1.2", "h3": "^1.12.0", "minimatch": "^9.0.4", "mlly": "^1.7.1", "mrmime": "^2.0.0", "open": "^10.1.0", "picocolors": "^1.0.1", "ws": "^8.17.1"}, "devDependencies": {"@antfu/eslint-config": "^2.21.1", "@iconify-json/carbon": "^1.1.36", "@iconify-json/file-icons": "^1.1.8", "@iconify-json/logos": "^1.1.43", "@iconify-json/ph": "^1.1.13", "@iconify-json/simple-icons": "^1.1.106", "@iconify-json/svg-spinners": "^1.1.2", "@iconify-json/twemoji": "^1.1.15", "@iconify-json/vscode-icons": "^1.1.35", "@nuxt/eslint": "^0.3.13", "@types/connect": "^3.4.38", "@types/ws": "^8.5.10", "@typescript-eslint/utils": "^7.13.1", "@unocss/eslint-config": "^0.61.0", "@unocss/nuxt": "^0.61.0", "@vueuse/nuxt": "^10.11.0", "eslint": "^9.5.0", "floating-vue": "^5.2.2", "fuse.js": "^7.0.0", "lint-staged": "^15.2.7", "nuxt": "^3.12.2", "nuxt-eslint-auto-explicit-import": "^0.0.2", "shiki": "^1.7.0", "simple-git-hooks": "^2.11.1", "textmate-grammar-glob": "^0.0.1", "typescript": "^5.5.2", "unbuild": "^2.0.0", "vue-tsc": "^2.0.21"}, "simple-git-hooks": {"pre-commit": "npx lint-staged"}, "lint-staged": {"*": "eslint --fix"}, "scripts": {"build": "nuxi build && unbuild", "dev": "nuxi dev", "start": "node bin.mjs", "lint": "nuxi prepare && eslint .", "typecheck": "vue-tsc --noEmit"}}