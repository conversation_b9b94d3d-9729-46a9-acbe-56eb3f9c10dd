(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory(require('vue-datepicker-next')) :
  typeof define === 'function' && define.amd ? define(['vue-datepicker-next'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, (global.DatePicker = global.DatePicker || {}, global.DatePicker.lang = global.DatePicker.lang || {}, global.DatePicker.lang.eo = factory(global.DatePicker)));
}(this, (function (DatePicker) { 'use strict';

  function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

  var DatePicker__default = /*#__PURE__*/_interopDefaultLegacy(DatePicker);

  var locale = {
    months: ['januaro', 'februaro', 'marto', 'aprilo', 'majo', 'junio', 'julio', 'aŭgusto', 'septembro', 'oktobro', 'novembro', 'decembro'],
    monthsShort: ['jan', 'feb', 'mar', 'apr', 'maj', 'jun', 'jul', 'aŭg', 'sep', 'okt', 'nov', 'dec'],
    weekdays: ['dimanĉo', 'lundo', 'mardo', 'merkredo', 'ĵaŭdo', 'vendredo', 'sabato'],
    weekdaysShort: ['dim', 'lun', 'mard', 'merk', 'ĵaŭ', 'ven', 'sab'],
    weekdaysMin: ['di', 'lu', 'ma', 'me', 'ĵa', 've', 'sa'],
    firstDayOfWeek: 1,
    firstWeekContainsDate: 7
  };

  const lang = {
      formatLocale: locale,
      yearFormat: 'YYYY',
      monthFormat: 'MMM',
      monthBeforeYear: true,
  };
  DatePicker__default['default'].locale('eo', lang);

  return lang;

})));
