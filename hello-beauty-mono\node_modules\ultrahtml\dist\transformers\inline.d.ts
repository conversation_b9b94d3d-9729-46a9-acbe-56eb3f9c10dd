// Generated by dts-bundle-generator v9.5.1

type Node$1 = DocumentNode | ElementNode | TextNode | CommentNode | DoctypeNode;
type NodeType = typeof DOCUMENT_NODE | typeof ELEMENT_NODE | typeof TEXT_NODE | typeof COMMENT_NODE | typeof DOCTYPE_NODE;
interface Location$1 {
	start: number;
	end: number;
}
interface BaseNode {
	type: NodeType;
	loc: [
		Location$1,
		Location$1
	];
	parent: Node$1;
	[key: string]: any;
}
interface LiteralNode extends BaseNode {
	value: string;
}
interface ParentNode$1 extends BaseNode {
	children: Node$1[];
}
interface DocumentNode extends Omit<ParentNode$1, "parent"> {
	type: typeof DOCUMENT_NODE;
	attributes: Record<string, string>;
	parent: undefined;
}
interface ElementNode extends ParentNode$1 {
	type: typeof ELEMENT_NODE;
	name: string;
	attributes: Record<string, string>;
}
interface TextNode extends LiteralNode {
	type: typeof TEXT_NODE;
}
interface CommentNode extends LiteralNode {
	type: typeof COMMENT_NODE;
}
interface DoctypeNode extends LiteralNode {
	type: typeof DOCTYPE_NODE;
}
declare const DOCUMENT_NODE = 0;
declare const ELEMENT_NODE = 1;
declare const TEXT_NODE = 2;
declare const COMMENT_NODE = 3;
declare const DOCTYPE_NODE = 4;
type Integer = number;
type Environment = {
	mediaType: "screen" | "print" | "not-screen-or-print";
	anyHover: "none" | "hover";
	anyPointer: "none" | "coarse" | "fine";
	colorGamut: "not-srgb" | "srgb-but-not-p3" | "p3-but-not-rec2020" | "rec2020";
	grid: "bitmap" | "grid";
	hover: "none" | "hover";
	overflowBlock: "none" | "scroll" | "paged";
	overflowInline: "none" | "scroll";
	pointer: "none" | "coarse" | "fine";
	scan: "interlace" | "progressive";
	update: "none" | "slow" | "fast";
	widthPx: Integer;
	heightPx: Integer;
	deviceWidthPx: Integer;
	deviceHeightPx: Integer;
	colorBits: Integer;
	monochromeBits: "not-monochrome" | Integer;
	colorIndex: "none" | Integer;
	dppx: Integer;
	displayMode: "fullscreen" | "standalone" | "minimal-ui" | "browser";
	dynamicRange: "not-hdr" | "hdr";
	environmentBlending: "opaque" | "additive" | "subtractive";
	forcedColors: "none" | "active";
	invertedColors: "none" | "inverted";
	navControls: "none" | "back";
	prefersColorScheme: "no-preference" | "light" | "dark";
	prefersContrast: "no-preference" | "less" | "more" | "custom";
	prefersReducedData: "no-preference" | "reduce";
	prefersReducedMotion: "no-preference" | "reduce";
	prefersReducedTransparency: "no-preference" | "reduce";
	scripting: "none" | "initial-only" | "enabled";
	videoColorGamut: "not-srgb" | "srgb-but-not-p3" | "p3-but-not-rec2020" | "rec2020";
	videoDynamicRange: "not-hdr" | "hdr";
	horizontalViewportSegments: Integer;
	verticalViewportSegments: Integer;
};
export interface InlineOptions {
	/** Emit `style` attributes as objects rather than strings. */
	useObjectSyntax: boolean;
	env: Partial<Environment> & {
		width: number;
		height: number;
	};
}
declare function inline(opts?: Partial<InlineOptions>): (doc: Node$1) => Node$1;

export {
	inline as default,
};

export {};
