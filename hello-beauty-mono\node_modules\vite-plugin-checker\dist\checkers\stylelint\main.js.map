{"version": 3, "sources": ["../../../src/checkers/stylelint/main.ts"], "sourcesContent": ["import path from 'node:path'\nimport { fileURLToPath } from 'node:url'\nimport { parentPort } from 'node:worker_threads'\nimport chokidar from 'chokidar'\nimport stylelint from 'stylelint'\nimport { Checker } from '../../Checker.js'\nimport { FileDiagnosticManager } from '../../FileDiagnosticManager.js'\nimport { createIgnore } from '../../glob.js'\nimport {\n  composeCheckerSummary,\n  consoleLog,\n  diagnosticToConsoleLevel,\n  diagnosticToRuntimeError,\n  diagnosticToTerminalLog,\n  filterLogLevel,\n  normalizeStylelintDiagnostic,\n  toClientPayload,\n} from '../../logger.js'\nimport { ACTION_TYPES, DiagnosticLevel } from '../../types.js'\nimport { translateOptions } from './options.js'\n\nconst manager = new FileDiagnosticManager()\nlet createServeAndBuild: any\n\nimport type { CreateDiagnostic } from '../../types.js'\n\nconst __filename = fileURLToPath(import.meta.url)\n\nconst createDiagnostic: CreateDiagnostic<'stylelint'> = (pluginConfig) => {\n  let overlay = true\n  let terminal = true\n\n  return {\n    config: async ({ enableOverlay, enableTerminal }) => {\n      overlay = enableOverlay\n      terminal = enableTerminal\n    },\n    async configureServer({ root }) {\n      if (!pluginConfig.stylelint) return\n\n      const translatedOptions = await translateOptions(\n        pluginConfig.stylelint.lintCommand,\n      )\n      const baseConfig = {\n        cwd: root,\n        ...translatedOptions,\n      } as const\n\n      const logLevel = (() => {\n        if (typeof pluginConfig.stylelint !== 'object') return undefined\n        const userLogLevel = pluginConfig.stylelint.dev?.logLevel\n        if (!userLogLevel) return undefined\n        const map = {\n          error: DiagnosticLevel.Error,\n          warning: DiagnosticLevel.Warning,\n        } as const\n\n        return userLogLevel.map((l) => map[l])\n      })()\n\n      const dispatchDiagnostics = () => {\n        const diagnostics = filterLogLevel(manager.getDiagnostics(), logLevel)\n\n        if (terminal) {\n          for (const d of diagnostics) {\n            consoleLog(\n              diagnosticToTerminalLog(d, 'Stylelint'),\n              diagnosticToConsoleLevel(d),\n            )\n          }\n\n          const errorCount = diagnostics.filter(\n            (d) => d.level === DiagnosticLevel.Error,\n          ).length\n          const warningCount = diagnostics.filter(\n            (d) => d.level === DiagnosticLevel.Warning,\n          ).length\n          consoleLog(\n            composeCheckerSummary('Stylelint', errorCount, warningCount),\n            errorCount ? 'error' : warningCount ? 'warn' : 'info',\n          )\n        }\n\n        if (overlay) {\n          parentPort?.postMessage({\n            type: ACTION_TYPES.overlayError,\n            payload: toClientPayload(\n              'stylelint',\n              diagnostics.map((d) => diagnosticToRuntimeError(d)),\n            ),\n          })\n        }\n      }\n\n      const handleFileChange = async (\n        filePath: string,\n        type: 'change' | 'unlink',\n      ) => {\n        const absPath = path.resolve(root, filePath)\n\n        if (type === 'unlink') {\n          manager.updateByFileId(absPath, [])\n        } else if (type === 'change') {\n          const { results: diagnosticsOfChangedFile } = await stylelint.lint({\n            ...baseConfig,\n            files: filePath,\n          })\n          const newDiagnostics = diagnosticsOfChangedFile.flatMap((d) =>\n            normalizeStylelintDiagnostic(d),\n          )\n          manager.updateByFileId(absPath, newDiagnostics)\n        }\n\n        dispatchDiagnostics()\n      }\n\n      // initial lint\n      const { results: diagnostics } = await stylelint.lint({\n        ...baseConfig,\n        ...pluginConfig.stylelint.dev?.overrideConfig,\n      })\n\n      manager.initWith(\n        diagnostics.flatMap((p) => normalizeStylelintDiagnostic(p)),\n      )\n      dispatchDiagnostics()\n\n      // watch lint\n      let watchTarget: string | string[] = root\n      if (pluginConfig.stylelint.watchPath) {\n        if (Array.isArray(pluginConfig.stylelint.watchPath)) {\n          watchTarget = pluginConfig.stylelint.watchPath.map((p) =>\n            path.resolve(root, p),\n          )\n        } else {\n          watchTarget = path.resolve(root, pluginConfig.stylelint.watchPath)\n        }\n      }\n\n      const watcher = chokidar.watch(watchTarget, {\n        cwd: root,\n        ignored: createIgnore(root, translatedOptions.files),\n      })\n\n      watcher.on('change', async (filePath) => {\n        handleFileChange(filePath, 'change')\n      })\n      watcher.on('unlink', async (filePath) => {\n        handleFileChange(filePath, 'unlink')\n      })\n    },\n  }\n}\n\nexport class StylelintChecker extends Checker<'stylelint'> {\n  public constructor() {\n    super({\n      name: 'stylelint',\n      absFilePath: __filename,\n      build: {\n        buildBin: (pluginConfig) => {\n          if (pluginConfig.stylelint) {\n            const { lintCommand } = pluginConfig.stylelint\n            return ['stylelint', lintCommand.split(' ').slice(1)]\n          }\n          return ['stylelint', ['']]\n        },\n      },\n      createDiagnostic,\n    })\n  }\n\n  public init() {\n    const _createServeAndBuild = super.initMainThread()\n    createServeAndBuild = _createServeAndBuild\n    super.initWorkerThread()\n  }\n}\n\nexport { createServeAndBuild }\nconst stylelintChecker = new StylelintChecker()\nstylelintChecker.prepare()\nstylelintChecker.init()\n"], "mappings": "AAAA,OAAO,UAAU;AACjB,SAAS,qBAAqB;AAC9B,SAAS,kBAAkB;AAC3B,OAAO,cAAc;AACrB,OAAO,eAAe;AACtB,SAAS,eAAe;AACxB,SAAS,6BAA6B;AACtC,SAAS,oBAAoB;AAC7B;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP,SAAS,cAAc,uBAAuB;AAC9C,SAAS,wBAAwB;AAEjC,MAAM,UAAU,IAAI,sBAAsB;AAC1C,IAAI;AAIJ,MAAM,aAAa,cAAc,YAAY,GAAG;AAEhD,MAAM,mBAAkD,CAAC,iBAAiB;AACxE,MAAI,UAAU;AACd,MAAI,WAAW;AAEf,SAAO;AAAA,IACL,QAAQ,OAAO,EAAE,eAAe,eAAe,MAAM;AACnD,gBAAU;AACV,iBAAW;AAAA,IACb;AAAA,IACA,MAAM,gBAAgB,EAAE,KAAK,GAAG;AArCpC;AAsCM,UAAI,CAAC,aAAa,UAAW;AAE7B,YAAM,oBAAoB,MAAM;AAAA,QAC9B,aAAa,UAAU;AAAA,MACzB;AACA,YAAM,aAAa;AAAA,QACjB,KAAK;AAAA,QACL,GAAG;AAAA,MACL;AAEA,YAAM,YAAY,MAAM;AAhD9B,YAAAA;AAiDQ,YAAI,OAAO,aAAa,cAAc,SAAU,QAAO;AACvD,cAAM,gBAAeA,MAAA,aAAa,UAAU,QAAvB,gBAAAA,IAA4B;AACjD,YAAI,CAAC,aAAc,QAAO;AAC1B,cAAM,MAAM;AAAA,UACV,OAAO,gBAAgB;AAAA,UACvB,SAAS,gBAAgB;AAAA,QAC3B;AAEA,eAAO,aAAa,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC;AAAA,MACvC,GAAG;AAEH,YAAM,sBAAsB,MAAM;AA5DxC,YAAAA;AA6DQ,cAAMC,eAAc,eAAe,QAAQ,eAAe,GAAG,QAAQ;AAErE,YAAI,UAAU;AACZ,qBAAW,KAAKA,cAAa;AAC3B;AAAA,cACE,wBAAwB,GAAG,WAAW;AAAA,cACtC,yBAAyB,CAAC;AAAA,YAC5B;AAAA,UACF;AAEA,gBAAM,aAAaA,aAAY;AAAA,YAC7B,CAAC,MAAM,EAAE,UAAU,gBAAgB;AAAA,UACrC,EAAE;AACF,gBAAM,eAAeA,aAAY;AAAA,YAC/B,CAAC,MAAM,EAAE,UAAU,gBAAgB;AAAA,UACrC,EAAE;AACF;AAAA,YACE,sBAAsB,aAAa,YAAY,YAAY;AAAA,YAC3D,aAAa,UAAU,eAAe,SAAS;AAAA,UACjD;AAAA,QACF;AAEA,YAAI,SAAS;AACX,WAAAD,MAAA,+BAAAA,IAAY,YAAY;AAAA,YACtB,MAAM,aAAa;AAAA,YACnB,SAAS;AAAA,cACP;AAAA,cACAC,aAAY,IAAI,CAAC,MAAM,yBAAyB,CAAC,CAAC;AAAA,YACpD;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,YAAM,mBAAmB,OACvB,UACA,SACG;AACH,cAAM,UAAU,KAAK,QAAQ,MAAM,QAAQ;AAE3C,YAAI,SAAS,UAAU;AACrB,kBAAQ,eAAe,SAAS,CAAC,CAAC;AAAA,QACpC,WAAW,SAAS,UAAU;AAC5B,gBAAM,EAAE,SAAS,yBAAyB,IAAI,MAAM,UAAU,KAAK;AAAA,YACjE,GAAG;AAAA,YACH,OAAO;AAAA,UACT,CAAC;AACD,gBAAM,iBAAiB,yBAAyB;AAAA,YAAQ,CAAC,MACvD,6BAA6B,CAAC;AAAA,UAChC;AACA,kBAAQ,eAAe,SAAS,cAAc;AAAA,QAChD;AAEA,4BAAoB;AAAA,MACtB;AAGA,YAAM,EAAE,SAAS,YAAY,IAAI,MAAM,UAAU,KAAK;AAAA,QACpD,GAAG;AAAA,QACH,IAAG,kBAAa,UAAU,QAAvB,mBAA4B;AAAA,MACjC,CAAC;AAED,cAAQ;AAAA,QACN,YAAY,QAAQ,CAAC,MAAM,6BAA6B,CAAC,CAAC;AAAA,MAC5D;AACA,0BAAoB;AAGpB,UAAI,cAAiC;AACrC,UAAI,aAAa,UAAU,WAAW;AACpC,YAAI,MAAM,QAAQ,aAAa,UAAU,SAAS,GAAG;AACnD,wBAAc,aAAa,UAAU,UAAU;AAAA,YAAI,CAAC,MAClD,KAAK,QAAQ,MAAM,CAAC;AAAA,UACtB;AAAA,QACF,OAAO;AACL,wBAAc,KAAK,QAAQ,MAAM,aAAa,UAAU,SAAS;AAAA,QACnE;AAAA,MACF;AAEA,YAAM,UAAU,SAAS,MAAM,aAAa;AAAA,QAC1C,KAAK;AAAA,QACL,SAAS,aAAa,MAAM,kBAAkB,KAAK;AAAA,MACrD,CAAC;AAED,cAAQ,GAAG,UAAU,OAAO,aAAa;AACvC,yBAAiB,UAAU,QAAQ;AAAA,MACrC,CAAC;AACD,cAAQ,GAAG,UAAU,OAAO,aAAa;AACvC,yBAAiB,UAAU,QAAQ;AAAA,MACrC,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAEO,MAAM,yBAAyB,QAAqB;AAAA,EAClD,cAAc;AACnB,UAAM;AAAA,MACJ,MAAM;AAAA,MACN,aAAa;AAAA,MACb,OAAO;AAAA,QACL,UAAU,CAAC,iBAAiB;AAC1B,cAAI,aAAa,WAAW;AAC1B,kBAAM,EAAE,YAAY,IAAI,aAAa;AACrC,mBAAO,CAAC,aAAa,YAAY,MAAM,GAAG,EAAE,MAAM,CAAC,CAAC;AAAA,UACtD;AACA,iBAAO,CAAC,aAAa,CAAC,EAAE,CAAC;AAAA,QAC3B;AAAA,MACF;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEO,OAAO;AACZ,UAAM,uBAAuB,MAAM,eAAe;AAClD,0BAAsB;AACtB,UAAM,iBAAiB;AAAA,EACzB;AACF;AAGA,MAAM,mBAAmB,IAAI,iBAAiB;AAC9C,iBAAiB,QAAQ;AACzB,iBAAiB,KAAK;", "names": ["_a", "diagnostics"]}