var VueDemi=function(n,e,f){if(n.install)return n;if(!e)return console.error("[vue-demi] no Vue instance found, please be sure to import `vue` before `vue-demi`."),n;if(e.version.slice(0,4)==="2.7."){let r=function(s,u){var l,c={},a={config:e.config,use:e.use.bind(e),mixin:e.mixin.bind(e),component:e.component.bind(e),provide:function(t,i){return c[t]=i,this},directive:function(t,i){return i?(e.directive(t,i),a):e.directive(t)},mount:function(t,i){return l||(l=new e(Object.assign({propsData:u},s,{provide:Object.assign(c,s.provide)})),l.$mount(t,i),l)},unmount:function(){l&&(l.$destroy(),l=void 0)}};return a};var v=r;for(var o in e)n[o]=e[o];n.isVue2=!0,n.isVue3=!1,n.install=function(){},n.Vue=e,n.Vue2=e,n.version=e.version,n.warn=e.util.warn,n.hasInjectionContext=function(){return!!n.getCurrentInstance()},n.createApp=r}else if(e.version.slice(0,2)==="2.")if(f){for(var o in f)n[o]=f[o];n.isVue2=!0,n.isVue3=!1,n.install=function(){},n.Vue=e,n.Vue2=e,n.version=e.version,n.hasInjectionContext=function(){return!!n.getCurrentInstance()}}else console.error("[vue-demi] no VueCompositionAPI instance found, please be sure to import `@vue/composition-api` before `vue-demi`.");else if(e.version.slice(0,2)==="3."){for(var o in e)n[o]=e[o];n.isVue2=!1,n.isVue3=!0,n.install=function(){},n.Vue=e,n.Vue2=void 0,n.version=e.version,n.set=function(r,s,u){return Array.isArray(r)?(r.length=Math.max(r.length,s),r.splice(s,1,u),u):(r[s]=u,u)},n.del=function(r,s){if(Array.isArray(r)){r.splice(s,1);return}delete r[s]}}else console.error("[vue-demi] Vue version "+e.version+" is unsupported.");return n}((globalThis||self).VueDemi=(globalThis||self).VueDemi||(typeof VueDemi<"u"?VueDemi:{}),(globalThis||self).Vue||(typeof Vue<"u"?Vue:void 0),(globalThis||self).VueCompositionAPI||(typeof VueCompositionAPI<"u"?VueCompositionAPI:void 0));(function(n,e,f,o){"use strict";function v(r,s,u){const l=()=>{var t,i;return new e((t=o.toValue(s))!=null?t:[],(i=o.toValue(u))==null?void 0:i.fuseOptions)},c=f.ref(l());f.watch(()=>{var t;return(t=o.toValue(u))==null?void 0:t.fuseOptions},()=>{c.value=l()},{deep:!0}),f.watch(()=>o.toValue(s),t=>{c.value.setCollection(t)},{deep:!0});const a=f.computed(()=>{const t=o.toValue(u);if(t?.matchAllWhenSearchEmpty&&!o.toValue(r))return o.toValue(s).map((d,p)=>({item:d,refIndex:p}));const i=t?.resultLimit;return c.value.search(o.toValue(r),i?{limit:i}:void 0)});return{fuse:c,results:a}}n.useFuse=v})(this.VueUse=this.VueUse||{},Fuse,VueDemi,VueUse);
