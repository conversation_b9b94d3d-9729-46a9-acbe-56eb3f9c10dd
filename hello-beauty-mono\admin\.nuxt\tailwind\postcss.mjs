// generated by the @nuxtjs/tailwindcss <https://github.com/nuxt-modules/tailwindcss> module at 8/9/2025, 10:26:47 AM
import "@nuxtjs/tailwindcss/config-ctx"
import configMerger from "@nuxtjs/tailwindcss/merger";

import cfg2 from "./../../tailwind.config.js";
const config = [
{"content":{"files":["E:/HelloBeauty-New/hello-beauty-mono/admin/components/**/*.{vue,js,jsx,mjs,ts,tsx}","E:/HelloBeauty-New/hello-beauty-mono/admin/components/**/*.{vue,js,jsx,mjs,ts,tsx}","E:/HelloBeauty-New/hello-beauty-mono/admin/layouts/**/*.{vue,js,jsx,mjs,ts,tsx}","E:/HelloBeauty-New/hello-beauty-mono/admin/plugins/**/*.{js,ts,mjs}","E:/HelloBeauty-New/hello-beauty-mono/admin/composables/**/*.{js,ts,mjs}","E:/HelloBeauty-New/hello-beauty-mono/admin/composables/*/index.{ts,js,mjs,mts}/**/*.{js,ts,mjs}","E:/HelloBeauty-New/hello-beauty-mono/admin/composables/**/**/*.{js,ts,mjs}","E:/HelloBeauty-New/hello-beauty-mono/admin/utils/**/*.{js,ts,mjs}","E:/HelloBeauty-New/hello-beauty-mono/admin/pages/**/*.{vue,js,jsx,mjs,ts,tsx}","E:/HelloBeauty-New/hello-beauty-mono/admin/{A,a}pp.{vue,js,jsx,mjs,ts,tsx}","E:/HelloBeauty-New/hello-beauty-mono/admin/{E,e}rror.{vue,js,jsx,mjs,ts,tsx}","E:/HelloBeauty-New/hello-beauty-mono/admin/app.config.{js,ts,mjs}"]}},
{},
cfg2
].reduce((acc, curr) => configMerger(acc, curr), {});

const resolvedConfig = config;

export default resolvedConfig;