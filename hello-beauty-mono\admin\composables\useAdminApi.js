export const useAdminApi = () => {
  const config = useRuntimeConfig()
  const auth = useAuth()

  const baseURL = config.public.API_URL || 'https://dev-api.hellobeauty.id'

  const apiCall = async (endpoint, options = {}) => {
    const { method = 'GET', body, headers = {} } = options

    const defaultHeaders = {
      'Authorization': `Bearer ${auth.tokenV0y}`,
      ...headers
    }

    // Don't set Content-Type for FormData, let the browser set it
    if (body instanceof FormData) {
      delete defaultHeaders['Content-Type']
    } else if (body && typeof body === 'object') {
      defaultHeaders['Content-Type'] = 'application/json'
    }

    try {
      const response = await $fetch(endpoint, {
        method,
        baseURL,
        headers: defaultHeaders,
        body: body instanceof FormData ? body : (body ? JSON.stringify(body) : undefined)
      })

      return response
    } catch (error) {
      console.error('API Error:', error)
      throw error
    }
  }

  return {
    get: (endpoint, options = {}) => apiCall(endpoint, { ...options, method: 'GET' }),
    post: (endpoint, body, options = {}) => apiCall(endpoint, { ...options, method: 'POST', body }),
    put: (endpoint, body, options = {}) => apiCall(endpoint, { ...options, method: 'PUT', body }),
    patch: (endpoint, body, options = {}) => apiCall(endpoint, { ...options, method: 'PATCH', body }),
    delete: (endpoint, options = {}) => apiCall(endpoint, { ...options, method: 'DELETE' })
  }
}
