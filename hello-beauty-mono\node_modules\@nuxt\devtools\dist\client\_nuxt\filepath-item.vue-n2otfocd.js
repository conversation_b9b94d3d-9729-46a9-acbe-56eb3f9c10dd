import{J as d,H as h,ax as f}from"./er2hcepb.js";import{k as v,l as c,Q as o,M as k,$ as y,S as t,F as l,a6 as i,a5 as s,U as B,W as C,Y as N,u as g,a7 as b}from"./vendor/json-editor-vue-gv8v06ez.js";const w={key:0,op50:""},I=v({__name:"FilepathItem",props:{filepath:{},lineBreak:{type:Boolean},subpath:{type:Boolean},override:{}},setup(u){const n=u,m=d(),r=h(),a=c(()=>n.filepath&&r.value?f(n.filepath,r.value.rootDir):{path:n.filepath||""});return(e,p)=>(o(),k(b(e.filepath?"button":"span"),{class:N([e.filepath?"hover:underline":"",e.lineBreak?"":"ws-nowrap of-hidden truncate"]),"font-mono":"",title:e.override||e.filepath,onClick:p[0]||(p[0]=D=>e.filepath&&g(m)(e.filepath))},{default:y(()=>[e.override?(o(),t(l,{key:0},[i(s(e.override),1)],64)):a.value.moduleName?(o(),t(l,{key:1},[B("span",null,s(a.value.moduleName),1),e.subpath?(o(),t("span",w,s(a.value.path.slice(a.value.moduleName.length)),1)):C("",!0)],64)):(o(),t(l,{key:2},[i(s(a.value.path),1)],64))]),_:1},8,["class","title"]))}});export{I as _};
