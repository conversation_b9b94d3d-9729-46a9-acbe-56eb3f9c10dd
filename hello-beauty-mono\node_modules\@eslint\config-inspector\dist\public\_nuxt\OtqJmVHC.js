import{f as O,m as P,g as A,c as v,F as N,q as B,o as p,Y as D,a,W as M,n as U,A as $,Z as R,d as S,t as _,j as y,G as c,C as o,r as j,I as G,O as L,x as u,b as V,w as T,s as h,l as w}from"./DPZgtMco.js";import{F as q,a as z,d as E}from"./CYHNKcvh.js";import{g as F}from"./BhsQKDyr.js";const I={flex:"~ inline gap-1 wrap","of-hidden":"","text-sm":""},W=["title"],Y=["value","title"],Z=O({__name:"OptionSelectGroup",props:P({options:{},titles:{},classes:{},props:{}},{modelValue:{type:[String,Number]},modelModifiers:{}}),emits:["update:modelValue"],setup(C){const f=A(C,"modelValue");return(r,g)=>(p(),v("fieldset",I,[(p(!0),v(N,null,B(r.options,(m,n)=>{var b,k,e,t,s,i,x,l;return p(),v("label",D({key:m,border:"~ base rounded-full",relative:"","hover:bg-hover":"","px2.5":"","py0.5":"",class:[m===f.value?"bg-active":"saturate-0 hover:saturate-100",((k=(b=r.props)==null?void 0:b[n])==null?void 0:k.class)||""],ref_for:!0},(e=r.props)==null?void 0:e[n],{title:(t=r.titles)==null?void 0:t[n]}),[a("div",{class:U([m===f.value?"":"op50",(s=r.titles)!=null&&s[n]?"":"capitalize",((i=r.classes)==null?void 0:i[n])||""])},[M(r.$slots,"default",{value:m,title:(x=r.titles)==null?void 0:x[n],index:n},()=>{var d;return[S(_(((d=r.titles)==null?void 0:d[n])??m),1)]})],2),$(a("input",{"onUpdate:modelValue":g[0]||(g[0]=d=>f.value=d),type:"radio",value:m,title:(l=r.titles)==null?void 0:l[n],absolute:"","inset-0":"","op-0.1":""},null,8,Y),[[R,f.value]])],16,W)}),128))]))}}),H={py4:"",flex:"~ col gap-2"},J={relative:"",flex:""},K=a("div",{absolute:"","bottom-0":"","left-0":"","top-0":"",flex:"~ items-center justify-center",p4:"",op50:""},[a("div",{"i-ph-magnifying-glass-duotone":""})],-1),Q={grid:"~ cols-[max-content_1fr] gap-2",my2:"","items-center":""},X=a("div",{"text-right":"","text-sm":"",op50:""}," Plugins ",-1),ee=a("div",{"text-right":"","text-sm":"",op50:""}," Usage ",-1),te={class:"flex items-center"},le={"ml--1":"","mr-1":"",flex:"","items-center":""},oe=a("div",{"text-right":"","text-sm":"",op50:""}," State ",-1),se={flex:"","items-center":"","gap-1":""},ae={key:0,"i-ph-check-square-duotone":"","ml--0.5":"","text-green":""},re={key:1,"i-ph-wrench-duotone":"","ml--0.5":"","text-amber":""},ne={key:2,"i-ph-prohibit-inset-duotone":"","ml--1":"","text-gray":""},ie={flex:"~ gap-2"},ue={flex:"~ inline gap-2 items-center",border:"~ gray/20 rounded-full","bg-gray:10":"",px3:"",py1:""},de=a("div",{"i-ph-list-checks-duotone":""},null,-1),pe={op75:""},ce={"text-sm":"",op50:""},fe=a("div",{"i-ph-funnel-duotone":"","text-purple":""},null,-1),me=a("span",{op50:""},"Clear Filter",-1),ve=a("button",{"i-ph-x":"","ml--1":"","text-sm":"",op25:"","hover:op100":""},null,-1),ge=[fe,me,ve],ye=O({__name:"rules",setup(C){const f=y(()=>Object.values(c.value.rules)),r=y(()=>Array.from(new Set(f.value.map(e=>e.plugin))).filter(Boolean)),g=y(()=>{let e=f.value;switch(o.plugin&&(e=e.filter(t=>t.plugin===o.plugin)),o.fixable!=null&&(e=e.filter(t=>!!t.fixable===o.fixable)),o.state){case"using":e=e.filter(t=>c.value.ruleToState.get(t.name));break;case"unused":e=e.filter(t=>!c.value.ruleToState.get(t.name));break;case"overloads":e=e.filter(t=>{var s;return(((s=c.value.ruleToState.get(t.name))==null?void 0:s.length)||0)>1});break;case"error":e=e.filter(t=>{var s;return(s=c.value.ruleToState.get(t.name))==null?void 0:s.some(i=>i.level==="error")});break;case"warn":e=e.filter(t=>{var s;return(s=c.value.ruleToState.get(t.name))==null?void 0:s.some(i=>i.level==="warn")});break;case"off":e=e.filter(t=>{var s;return(s=c.value.ruleToState.get(t.name))==null?void 0:s.some(i=>i.level==="off")});break;case"off-only":e=e.filter(t=>{const s=c.value.ruleToState.get(t.name);return s!=null&&s.length?s.every(i=>i.level==="off"):!1});break}switch(o.status){case"active":e=e.filter(t=>!t.deprecated);break;case"recommended":e=e.filter(t=>{var s;return(s=t.docs)==null?void 0:s.recommended});break;case"fixable":e=e.filter(t=>t.fixable);break;case"deprecated":e=e.filter(t=>t.deprecated);break}return e}),m=y(()=>new q(g.value,{keys:["name","docs.description"],threshold:.5})),n=j(g.value);G(()=>[o.search,g.value],()=>{if(!o.search)return n.value=g.value;n.value=m.value.search(o.search).map(e=>e.item)},{debounce:200});const b=y(()=>!(o.search||o.plugin||o.state!=="using"||o.status!=="active"));function k(){o.search="",o.plugin="",o.state="using",o.status="active"}return(e,t)=>{const s=Z,i=E,x=z;return p(),v("div",null,[a("div",H,[a("div",J,[$(a("input",{"onUpdate:modelValue":t[0]||(t[0]=l=>u(o).search=l),class:U(u(o).search?"font-mono":""),placeholder:"Search rules...",border:"~ base rounded-full","w-full":"","bg-transparent":"",px3:"",py2:"",pl10:"","outline-none":""},null,2),[[L,u(o).search]]),K]),a("div",Q,[X,V(s,{modelValue:u(o).plugin,"onUpdate:modelValue":t[1]||(t[1]=l=>u(o).plugin=l),options:["",...r.value],titles:["All",...r.value],props:[{},...r.value.map(l=>({class:"font-mono",style:u(o).plugin===l?{color:("getPluginColor"in e?e.getPluginColor:u(F))(l),backgroundColor:("getPluginColor"in e?e.getPluginColor:u(F))(l,.1)}:{}}))]},null,8,["modelValue","options","titles","props"]),ee,V(s,{modelValue:u(o).state,"onUpdate:modelValue":t[2]||(t[2]=l=>u(o).state=l),options:["","using","unused","error","warn","off","overloads","off-only"],titles:["All","Using","Unused","Error","Warn","Off","Overloaded","Off Only"]},{default:T(({value:l,title:d})=>[a("div",te,[a("div",le,[l==="error"||l==="overloads"?(p(),w(i,{key:0,level:"error"})):h("",!0),l==="warn"||l==="overloads"?(p(),w(i,{key:1,level:"warn"})):h("",!0),l==="off"||l==="off-only"||l==="overloads"?(p(),w(i,{key:2,level:"off"})):h("",!0)]),S(" "+_(d||l),1)])]),_:1},8,["modelValue"]),oe,V(s,{modelValue:u(o).status,"onUpdate:modelValue":t[3]||(t[3]=l=>u(o).status=l),options:["","active","recommended","fixable","deprecated"],titles:["All","Active","Recommended","Fixable","Deprecated"]},{default:T(({value:l,title:d})=>[a("div",se,[l==="recommended"?(p(),v("div",ae)):h("",!0),l==="fixable"?(p(),v("div",re)):h("",!0),l==="deprecated"?(p(),v("div",ne)):h("",!0),S(" "+_(d||l),1)])]),_:1},8,["modelValue"])])]),a("div",ie,[a("div",ue,[de,a("span",null,_(n.value.length),1),a("span",pe,"rules "+_(b.value?"enabled":"filtered"),1),a("span",ce,"out of "+_(f.value.length)+" rules",1)]),b.value?h("",!0):(p(),v("button",{key:0,flex:"~ inline gap-2 items-center",border:"~ purple/20 rounded-full","bg-purple:10":"",px3:"",py1:"",onClick:t[4]||(t[4]=l=>k())},ge))]),V(x,{my4:"",rules:n.value,"get-bind":l=>{var d;return{class:(d=u(c).ruleToState.get(l))!=null&&d.length||u(o).state==="unused"?"":"op40"}}},null,8,["rules","get-bind"])])}}});export{ye as default};
