!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("cronstrue")):"function"==typeof define&&define.amd?define("locales/da.min",["cronstrue"],t):"object"==typeof exports?exports["locales/da.min"]=t(require("cronstrue")):e["locales/da.min"]=t(e.cronstrue)}(globalThis,(e=>(()=>{"use strict";var t={93:t=>{t.exports=e}},r={};function n(e){var o=r[e];if(void 0!==o)return o.exports;var u=r[e]={exports:{}};return t[e](u,u.exports,n),u.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var o={};n.r(o);var u=n(93),p=n.n(u),i=o;Object.defineProperty(i,"__esModule",{value:!0}),i.da=void 0;var s=function(){function e(){}return e.prototype.use24HourTimeFormatByDefault=function(){return!0},e.prototype.anErrorOccuredWhenGeneratingTheExpressionD=function(){return"Der opstod en fejl ved generering af udtryksbeskrivelsen. Tjek cron-ekspressionssyntaxen."},e.prototype.at=function(){return"kl"},e.prototype.atSpace=function(){return"kl "},e.prototype.atX0=function(){return"kl %s"},e.prototype.atX0MinutesPastTheHour=function(){return"%s minutter efter timeskift"},e.prototype.atX0SecondsPastTheMinute=function(){return"%s sekunder efter minutskift"},e.prototype.betweenX0AndX1=function(){return"mellem %s og %s"},e.prototype.commaBetweenDayX0AndX1OfTheMonth=function(){return", mellem dag %s og %s i måneden"},e.prototype.commaEveryDay=function(){return", hver dag"},e.prototype.commaEveryX0Days=function(){return", hver %s. dag"},e.prototype.commaEveryX0DaysOfTheWeek=function(){return", hver %s. ugedag"},e.prototype.commaEveryX0Months=function(){return", hver %s. måned"},e.prototype.commaEveryX0Years=function(){return", hvert %s. år"},e.prototype.commaOnDayX0OfTheMonth=function(){return", på dag %s i måneden"},e.prototype.commaOnlyInX0=function(){return", kun i %s"},e.prototype.commaOnlyOnX0=function(e){return", på enhver %s"},e.prototype.commaAndOnX0=function(){return", og på %s"},e.prototype.commaOnThe=function(){return", på den "},e.prototype.commaOnTheLastDayOfTheMonth=function(){return", på den sidste dag i måneden"},e.prototype.commaOnTheLastWeekdayOfTheMonth=function(){return", på den sidste hverdag i måneden"},e.prototype.commaDaysBeforeTheLastDayOfTheMonth=function(){return", %s dage før den sidste dag i måneden"},e.prototype.commaOnTheLastX0OfTheMonth=function(){return", på den sidste %s i måneden"},e.prototype.commaOnTheX0OfTheMonth=function(){return", på den %s i måneden"},e.prototype.commaX0ThroughX1=function(){return", %s til og med %s"},e.prototype.commaAndX0ThroughX1=function(){return", og %s til og med %s"},e.prototype.everyHour=function(){return"hver time"},e.prototype.everyMinute=function(){return"hvert minut"},e.prototype.everyMinuteBetweenX0AndX1=function(){return"hvert minut mellem %s og %s"},e.prototype.everySecond=function(){return"hvert sekund"},e.prototype.everyX0Hours=function(){return"hver %s. time"},e.prototype.everyX0Minutes=function(){return"hvert %s. minut"},e.prototype.everyX0Seconds=function(){return"hvert %s. sekund"},e.prototype.fifth=function(){return"femte"},e.prototype.first=function(){return"første"},e.prototype.firstWeekday=function(){return"første hverdag"},e.prototype.fourth=function(){return"fjerde"},e.prototype.minutesX0ThroughX1PastTheHour=function(){return"minutterne fra %s til og med %s hver time"},e.prototype.second=function(){return"anden"},e.prototype.secondsX0ThroughX1PastTheMinute=function(){return"sekunderne fra %s til og med %s hvert minut"},e.prototype.spaceAnd=function(){return" og"},e.prototype.spaceX0OfTheMonth=function(){return" %s i måneden"},e.prototype.lastDay=function(){return"sidste dag"},e.prototype.third=function(){return"tredje"},e.prototype.weekdayNearestDayX0=function(){return"hverdag nærmest dag %s"},e.prototype.commaMonthX0ThroughMonthX1=function(){return null},e.prototype.commaYearX0ThroughYearX1=function(){return null},e.prototype.atX0MinutesPastTheHourGt20=function(){return null},e.prototype.atX0SecondsPastTheMinuteGt20=function(){return null},e.prototype.commaStartingX0=function(){return", startende %s"},e.prototype.daysOfTheWeek=function(){return["søndag","mandag","tirsdag","onsdag","torsdag","fredag","lørdag"]},e.prototype.monthsOfTheYear=function(){return["januar","februar","marts","april","maj","juni","juli","august","september","oktober","november","december"]},e}();return i.da=s,p().locales.da=new s,o})()));